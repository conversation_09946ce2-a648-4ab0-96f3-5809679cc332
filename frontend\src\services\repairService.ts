import api from './api';

export interface RepairRequest {
  name: string;
  email: string;
  phone: string;
  serviceType: string;
  repairType: string;
  description: string;
  
  // Laptop specific fields
  laptopBrand?: string;
  laptopModel?: string;
  laptopAge?: string;
  
  // Desktop specific fields
  desktopType?: string;
  operatingSystem?: string;
  
  // Tablet specific fields
  tabletBrand?: string;
  tabletModel?: string;
  tabletIssue?: string;
  
  // Phone specific fields
  phoneBrand?: string;
  phoneModel?: string;
  phoneCarrier?: string;
}

export interface RepairResponse {
  success: boolean;
  message: string;
  data?: {
    requestId: string;
    requestNumber: string;
    status: string;
    estimatedCost: number;
    submittedAt: string;
    customerEmail: string;
    serviceType: string;
    repairType: string;
    emailSent: boolean;
    warning?: string;
  };
  error?: string;
}

export interface RepairService {
  id: string;
  name: string;
  description: string;
  icon: string;
}

export interface RepairServicesResponse {
  success: boolean;
  data: {
    services: RepairService[];
    repairTypes: Record<string, string[]>;
  };
}

class RepairServiceAPI {
  // Submit a repair request
  async submitRepairRequest(repairData: RepairRequest): Promise<RepairResponse> {
    try {
      const response = await api.post<RepairResponse>('/repair/request', repairData);
      return response.data;
    } catch (error: any) {
      console.error('Error submitting repair request:', error);
      
      // Handle different error types
      if (error.response?.data) {
        throw new Error(error.response.data.message || 'Failed to submit repair request');
      } else if (error.request) {
        throw new Error('Network error: Unable to reach the server');
      } else {
        throw new Error('Failed to submit repair request');
      }
    }
  }

  // Get repair services and types
  async getRepairServices(): Promise<RepairServicesResponse> {
    try {
      console.log('🔧 RepairService: Making API call to /repair/services');
      const response = await api.get<RepairServicesResponse>('/repair/services');
      console.log('🔧 RepairService: API response:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('🔧 RepairService: Error fetching repair services:', error);
      console.error('🔧 RepairService: Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });
      throw new Error(error.response?.data?.message || 'Failed to fetch repair services');
    }
  }

  // Test email functionality (development only)
  async testEmail(): Promise<any> {
    try {
      const response = await api.post('/repair/test-email');
      return response.data;
    } catch (error: any) {
      console.error('Error testing email:', error);
      throw new Error(error.response?.data?.message || 'Failed to test email');
    }
  }

  // Validate repair request data
  validateRepairRequest(data: RepairRequest): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Required fields
    if (!data.name || data.name.length < 2) {
      errors.push('Name must be at least 2 characters');
    }

    if (!data.email || !this.isValidEmail(data.email)) {
      errors.push('Please enter a valid email address');
    }

    if (!data.phone || data.phone.length < 10) {
      errors.push('Please enter a valid phone number');
    }

    if (!data.serviceType) {
      errors.push('Please select a service type');
    }

    if (!data.repairType) {
      errors.push('Please select a repair type');
    }

    if (!data.description || data.description.length < 10) {
      errors.push('Description must be at least 10 characters');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  // Email validation helper
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  }

  // Format phone number
  formatPhoneNumber(phone: string): string {
    // Remove all non-digit characters
    const cleaned = phone.replace(/\D/g, '');
    
    // Format as (XXX) XXX-XXXX if it's a US number
    if (cleaned.length === 10) {
      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;
    }
    
    return phone; // Return original if not a standard format
  }

  // Get device-specific fields based on service type
  getDeviceSpecificFields(serviceType: string): string[] {
    switch (serviceType) {
      case 'laptop':
        return ['laptopBrand', 'laptopModel', 'laptopAge'];
      case 'desktop':
        return ['desktopType', 'operatingSystem'];
      case 'tablet':
        return ['tabletBrand', 'tabletModel', 'tabletIssue'];
      case 'phone':
        return ['phoneBrand', 'phoneModel', 'phoneCarrier'];
      default:
        return [];
    }
  }

  // Get repair types for a specific service
  getRepairTypesForService(serviceType: string): string[] {
    const repairTypes: Record<string, string[]> = {
      laptop: [
        'Screen Replacement',
        'Keyboard Repair',
        'Battery Replacement',
        'Hard Drive Replacement',
        'RAM Upgrade',
        'Virus Removal',
        'Operating System Installation',
        'Hardware Diagnosis',
        'Overheating Issues',
        'Power Jack Repair'
      ],
      desktop: [
        'Hardware Upgrade',
        'Virus Removal',
        'Operating System Installation',
        'Hard Drive Replacement',
        'RAM Installation',
        'Graphics Card Installation',
        'Power Supply Replacement',
        'Motherboard Repair',
        'CPU Replacement',
        'System Optimization'
      ],
      tablet: [
        'Screen Replacement',
        'Battery Replacement',
        'Charging Port Repair',
        'Software Issues',
        'Water Damage Repair',
        'Speaker Repair',
        'Camera Repair',
        'Button Repair',
        'Wi-Fi Issues',
        'Factory Reset'
      ],
      phone: [
        'Screen Replacement',
        'Battery Replacement',
        'Charging Port Repair',
        'Camera Repair',
        'Speaker Repair',
        'Water Damage Repair',
        'Software Issues',
        'Button Repair',
        'Microphone Repair',
        'Factory Reset'
      ]
    };

    return repairTypes[serviceType] || [];
  }
}

export default new RepairServiceAPI();
