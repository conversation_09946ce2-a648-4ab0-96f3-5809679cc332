@startuml TechStore_Admin_Product_Management

!theme plain
skinparam backgroundColor #FFFFFF
skinparam actor {
    BackgroundColor #E3F2FD
    BorderColor #1976D2
}
skinparam usecase {
    BackgroundColor #F3E5F5
    BorderColor #7B1FA2
}
skinparam package {
    BackgroundColor #FFF8E1
    BorderColor #F57C00
}

title TechStore - Gestion des Produits par l'Administrateur

actor "👨‍💼 Administrateur" as Admin
actor "📧 Système Email" as Email
actor "🗄️ Base de Données" as Database

rectangle "Admin Product Management System" {
    
    package "Authentification" {
        usecase "Se connecter Admin" as AdminLogin
        usecase "Vérifier permissions" as CheckPermissions
    }
    
    package "Gestion des Produits" {
        usecase "Consulter liste produits" as ViewProductList
        usecase "Rechercher produits" as SearchProducts
        usecase "Filtrer par catégorie" as FilterByCategory
        usecase "Ajouter nouveau produit" as AddProduct
        usecase "Modifier produit existant" as EditProduct
        usecase "Supprimer produit" as DeleteProduct
        usecase "Gérer stock" as ManageStock
        usecase "Définir prix/promotions" as ManagePricing
    }
    
    package "Gestion des Spécifications" {
        usecase "Sélectionner catégorie" as SelectCategory
        usecase "Charger template spécifications" as LoadSpecTemplate
        usecase "Configurer spécifications requises" as ConfigRequiredSpecs
        usecase "Configurer spécifications optionnelles" as ConfigOptionalSpecs
        usecase "Valider spécifications" as ValidateSpecs
        usecase "Sauvegarder spécifications" as SaveSpecs
    }
    
    package "Templates par Catégorie" {
        usecase "Template Laptops" as TemplateLaptops
        usecase "Template Desktops" as TemplateDesktops
        usecase "Template Components" as TemplateComponents
        usecase "Template Accessories" as TemplateAccessories
        usecase "Template Smartphones" as TemplateSmartphones
        usecase "Template TVs" as TemplateTVs
        usecase "Template Audio" as TemplateAudio
    }
    
    package "Validation & Sauvegarde" {
        usecase "Valider données produit" as ValidateProduct
        usecase "Vérifier unicité" as CheckUniqueness
        usecase "Sauvegarder en base" as SaveToDatabase
        usecase "Mettre à jour index recherche" as UpdateSearchIndex
        usecase "Notifier changements" as NotifyChanges
    }
}

' Relations principales Admin
Admin --> AdminLogin
Admin --> ViewProductList
Admin --> SearchProducts
Admin --> FilterByCategory
Admin --> AddProduct
Admin --> EditProduct
Admin --> DeleteProduct
Admin --> ManageStock
Admin --> ManagePricing

' Flux d'authentification
AdminLogin ..> CheckPermissions : <<include>>

' Flux de consultation
ViewProductList ..> SearchProducts : <<extend>>
ViewProductList ..> FilterByCategory : <<extend>>

' Flux d'ajout de produit
AddProduct --> SelectCategory
SelectCategory --> LoadSpecTemplate
LoadSpecTemplate --> ConfigRequiredSpecs
ConfigRequiredSpecs --> ConfigOptionalSpecs
ConfigOptionalSpecs --> ValidateSpecs
ValidateSpecs --> SaveSpecs
SaveSpecs --> ValidateProduct
ValidateProduct --> SaveToDatabase

' Flux de modification
EditProduct --> LoadSpecTemplate
EditProduct --> ValidateSpecs
EditProduct --> SaveSpecs

' Templates de spécifications
LoadSpecTemplate --> TemplateLaptops
LoadSpecTemplate --> TemplateDesktops
LoadSpecTemplate --> TemplateComponents
LoadSpecTemplate --> TemplateAccessories
LoadSpecTemplate --> TemplateSmartphones
LoadSpecTemplate --> TemplateTVs
LoadSpecTemplate --> TemplateAudio

' Validation et sauvegarde
ValidateProduct ..> CheckUniqueness : <<include>>
SaveToDatabase --> UpdateSearchIndex
SaveToDatabase --> NotifyChanges
SaveToDatabase --> Database

' Notifications
NotifyChanges --> Email

' Relations include/extend
AddProduct ..> ValidateProduct : <<include>>
EditProduct ..> ValidateProduct : <<include>>
DeleteProduct ..> CheckPermissions : <<include>>
ManageStock ..> SaveToDatabase : <<include>>
ManagePricing ..> ValidateProduct : <<include>>

' Notes explicatives
note right of TemplateLaptops
  Spécifications Laptops:
  • Requis: processor, memory, storage, display
  • Optionnel: graphics, battery, weight, ports
  • Validation: format, valeurs autorisées
end note

note right of TemplateDesktops
  Spécifications Desktops:
  • Requis: processor, memory, storage, graphics
  • Optionnel: cooling, motherboard, psu, case
  • Validation: compatibilité composants
end note

note right of TemplateComponents
  Spécifications Components:
  • Requis: type, model, specifications
  • Optionnel: compatibility, warranty, power
  • Validation: standards techniques
end note

note bottom of ValidateSpecs
  Validation automatique:
  • Champs requis présents
  • Format des valeurs
  • Cohérence technique
  • Standards industrie
end note

note bottom of SaveToDatabase
  Sauvegarde sécurisée:
  • Transaction atomique
  • Backup automatique
  • Log des modifications
  • Rollback en cas d'erreur
end note

@enduml
