import mongoose from 'mongoose';
import dotenv from 'dotenv';
import Product from './server/models/productModel.js';

// Load environment variables
dotenv.config();

const sampleProducts = [
  {
    name: 'ProBook X1 Ultra',
    description: 'Ultra-thin laptop with stunning 4K display and all-day battery life. Perfect for professionals and content creators.',
    price: 1299,
    category: 'laptops',
    image: 'https://images.unsplash.com/photo-1531297484001-80022131f5a1?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    stock: 15,
    isFeatured: true,
    isNewProduct: true,
    rating: 4.8,
    reviews: 124,
    specifications: new Map([
      ['processor', 'Intel Core i7-1165G7'],
      ['memory', '16GB DDR4'],
      ['storage', '512GB SSD'],
      ['display', '14" 4K UHD'],
      ['graphics', 'Intel Iris Xe'],
      ['battery', 'Up to 12 hours'],
      ['weight', '1.4 kg']
    ])
  },
  {
    name: 'TechStation Pro Workstation',
    description: 'Professional desktop workstation for demanding tasks. Built for developers, designers, and power users.',
    price: 1799,
    category: 'desktops',
    image: 'https://images.unsplash.com/photo-1461749280684-dccba630e2f6?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    stock: 8,
    isFeatured: true,
    rating: 4.7,
    reviews: 89,
    specifications: new Map([
      ['processor', 'AMD Ryzen 9 5900X'],
      ['memory', '32GB DDR4'],
      ['storage', '1TB NVMe SSD + 2TB HDD'],
      ['graphics', 'NVIDIA RTX 3080'],
      ['cooling', 'Liquid cooling'],
      ['motherboard', 'X570 Chipset'],
      ['psu', '850W 80+ Gold']
    ])
  },
  {
    name: 'Ultra Graphics Card RTX 4070',
    description: 'High-performance graphics card for gaming and professional design work. Ray tracing enabled.',
    price: 799,
    salePrice: 699,
    category: 'components',
    image: 'https://images.unsplash.com/photo-1518770660439-4636190af475?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    stock: 12,
    isFeatured: true,
    isOnSale: true,
    rating: 4.9,
    reviews: 215,
    specifications: new Map([
      ['type', 'Graphics Card'],
      ['model', 'NVIDIA RTX 4070'],
      ['specifications', '12GB GDDR6X, 2610 MHz Boost Clock'],
      ['power', '200W TDP'],
      ['compatibility', 'PCIe 4.0 x16'],
      ['dimensions', '300mm x 137mm x 61mm']
    ])
  },
  {
    name: 'ErgoMouse Pro Wireless',
    description: 'Ergonomic wireless mouse designed for extended comfort and precision. Perfect for office work.',
    price: 79,
    category: 'accessories',
    image: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    stock: 45,
    isFeatured: true,
    isNewProduct: true,
    rating: 4.6,
    reviews: 178,
    specifications: new Map([
      ['type', 'Wireless Mouse'],
      ['compatibility', 'Windows, macOS, Linux'],
      ['connectivity', 'Bluetooth 5.0 or 2.4GHz USB'],
      ['battery', 'Up to 70 hours'],
      ['dpi', '16,000 DPI optical sensor'],
      ['weight', '93g'],
      ['features', '7 programmable buttons']
    ])
  },
  {
    name: 'Gaming Laptop X7 Pro',
    description: 'Powerful gaming laptop with RGB keyboard and high refresh rate display. Built for serious gamers.',
    price: 1599,
    category: 'laptops',
    image: 'https://images.unsplash.com/photo-1488590528505-98d2b5aba04b?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    stock: 10,
    rating: 4.5,
    reviews: 92,
    specifications: new Map([
      ['processor', 'Intel Core i9-11900H'],
      ['memory', '32GB DDR4'],
      ['storage', '1TB NVMe SSD'],
      ['display', '15.6" 165Hz IPS'],
      ['graphics', 'NVIDIA RTX 3070'],
      ['keyboard', 'RGB Backlit Mechanical'],
      ['ports', '3x USB-A, 2x USB-C, HDMI 2.1']
    ])
  },
  {
    name: 'MiniPC Compact Desktop',
    description: 'Compact desktop PC perfect for everyday use, home office, and light productivity tasks.',
    price: 499,
    salePrice: 449,
    category: 'desktops',
    image: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    stock: 20,
    isOnSale: true,
    rating: 4.3,
    reviews: 67,
    specifications: new Map([
      ['processor', 'Intel Core i5-10400'],
      ['memory', '8GB DDR4'],
      ['storage', '256GB SSD'],
      ['graphics', 'Intel UHD Graphics 630'],
      ['connectivity', 'WiFi 6, Bluetooth 5.1'],
      ['case', 'Mini-ITX Form Factor'],
      ['os', 'Windows 11 Home']
    ])
  },
  {
    name: 'NVMe Ultra SSD 1TB',
    description: 'Ultra-fast NVMe SSD storage solution for demanding workloads and gaming. PCIe 4.0 compatible.',
    price: 149,
    category: 'components',
    image: 'https://images.unsplash.com/photo-1518770660439-4636190af475?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    stock: 35,
    rating: 4.7,
    reviews: 103,
    specifications: new Map([
      ['type', 'NVMe SSD'],
      ['model', 'PCIe 4.0 x4'],
      ['specifications', '1TB Capacity, 3D NAND Flash'],
      ['compatibility', 'M.2 2280 Form Factor'],
      ['power', '7W Active, 0.5W Idle'],
      ['warranty', '5 Years Limited']
    ])
  },
  {
    name: 'Premium Noise-Cancelling Headset',
    description: 'Professional noise-cancelling headset with crystal-clear audio and comfortable design.',
    price: 129,
    category: 'accessories',
    image: 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?ixlib=rb-1.2.1&auto=format&fit=crop&w=800&q=80',
    stock: 25,
    isNewProduct: true,
    rating: 4.6,
    reviews: 156,
    specifications: new Map([
      ['type', 'Over-ear Headset'],
      ['compatibility', 'Universal 3.5mm, USB-C'],
      ['drivers', '40mm Dynamic Drivers'],
      ['frequency', '20Hz-20kHz'],
      ['microphone', 'Detachable Boom Mic'],
      ['battery', 'Up to 24 hours ANC'],
      ['features', 'Active Noise Cancellation']
    ])
  }
];

const populateProducts = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI);
    console.log('Connected to MongoDB');

    // Clear existing products
    await Product.deleteMany({});
    console.log('Cleared existing products');

    // Insert sample products
    const createdProducts = await Product.insertMany(sampleProducts);
    console.log(`Created ${createdProducts.length} sample products`);

    console.log('Sample products:');
    createdProducts.forEach(product => {
      console.log(`- ${product.name} (${product.category}) - $${product.price}`);
    });

    process.exit(0);
  } catch (error) {
    console.error('Error populating products:', error);
    process.exit(1);
  }
};

populateProducts();
