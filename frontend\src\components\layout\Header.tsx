import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';
import { useIsMobile } from '@/hooks/use-mobile';
import { CartDrawer } from '@/components/cart/CartDrawer';
import { UserMenu } from '@/components/auth/UserMenu';
import { Logo } from '@/components/ui/Logo';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { SearchDialog } from '@/components/search/SearchDialog';
import { useWishlist } from '@/contexts/WishlistContext';
import { Menu, X, Heart } from 'lucide-react';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();
  const isMobile = useIsMobile();
  const { getWishlistCount } = useWishlist();

  // Close mobile menu when route changes
  useEffect(() => {
    setIsMenuOpen(false);
  }, [location.pathname]);

  // Add scroll listener
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${
        isScrolled ? 'bg-background/80 backdrop-blur-sm shadow-sm' : 'bg-background/50 backdrop-blur-none'
      }`}
    >
      <div className="container flex h-16 items-center justify-between">
        {/* Logo */}
        <div className="flex items-center">
          <Link to="/" className="mr-6">
            <Logo />
          </Link>
        </div>

        {/* Desktop Navigation */}
        <nav className="hidden md:flex items-center space-x-1">
          <Link to="/">
            <Button variant="ghost">Home</Button>
          </Link>
          <Link to="/products">
            <Button variant="ghost">Products</Button>
          </Link>
          <Link to="/repair">
            <Button variant="ghost">Repair Services</Button>
          </Link>
          <Link to="/crawler">
            <Button variant="ghost">Crawler</Button>
          </Link>
          <Link to="/about">
            <Button variant="ghost">About</Button>
          </Link>
          <Link to="/contact">
            <Button variant="ghost">Contact</Button>
          </Link>
        </nav>

        {/* Actions on right side */}
        <div className="flex items-center space-x-2">
          <SearchDialog />

          {/* Wishlist Button */}
          <Link to="/profile?tab=wishlist">
            <Button variant="ghost" size="icon" className="relative">
              <Heart className="h-5 w-5" />
              {getWishlistCount() > 0 && (
                <Badge
                  variant="destructive"
                  className="absolute -top-2 -right-2 h-5 w-5 rounded-full p-0 flex items-center justify-center text-xs"
                >
                  {getWishlistCount()}
                </Badge>
              )}
            </Button>
          </Link>

          <CartDrawer />
          <UserMenu />
          
          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="icon"
            onClick={toggleMenu}
            className="md:hidden"
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </Button>
        </div>
      </div>

      {/* Mobile Navigation */}
      <AnimatePresence>
        {isMenuOpen && isMobile && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="md:hidden bg-background border-b overflow-hidden"
          >
            <nav className="container py-4 flex flex-col space-y-1">
              <Link to="/">
                <Button variant="ghost" className="w-full justify-start">Home</Button>
              </Link>
              <Link to="/products">
                <Button variant="ghost" className="w-full justify-start">Products</Button>
              </Link>
              <Link to="/repair">
                <Button variant="ghost" className="w-full justify-start">Repair Services</Button>
              </Link>
              <Link to="/crawler">
                <Button variant="ghost" className="w-full justify-start">Crawler</Button>
              </Link>
              <Link to="/about">
                <Button variant="ghost" className="w-full justify-start">About</Button>
              </Link>
              <Link to="/contact">
                <Button variant="ghost" className="w-full justify-start">Contact</Button>
              </Link>
            </nav>
          </motion.div>
        )}
      </AnimatePresence>
    </header>
  );
};

export default Header;
