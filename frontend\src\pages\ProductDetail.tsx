import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import Header from '@/components/layout/Header';
import Footer from '@/components/layout/Footer';
import { AnimatedButton } from '@/components/ui/AnimatedButton';
import { WishlistButton } from '@/components/ui/WishlistButton';
import { useProduct } from '@/hooks/useProducts';
import { useCart } from '@/contexts/CartContext';
import { CartDrawer } from '@/components/cart/CartDrawer';
import ProductSpecifications from '@/components/products/ProductSpecifications';
import { 
  Minus, 
  Plus, 
  Heart, 
  Share2, 
  Truck, 
  ShieldCheck, 
  RefreshCw,
  ChevronRight,
  ShoppingCart
} from 'lucide-react';

const ProductDetail = () => {
  const { id } = useParams<{ id: string }>();
  const { data: product, isLoading, error } = useProduct(id || '');
  const [quantity, setQuantity] = useState(1);
  const [selectedImage, setSelectedImage] = useState(0);
  const [isCartOpen, setIsCartOpen] = useState(false);
  const { addToCart } = useCart();

  // If no ID is provided, show error immediately
  if (!id) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 pt-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16 text-center">
            <h1 className="text-2xl font-bold mb-4">Invalid Product URL</h1>
            <p className="text-muted-foreground mb-8">No product ID was provided in the URL.</p>
            <Link to="/products">
              <AnimatedButton>Return to Products</AnimatedButton>
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }
  
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const handleAddToCart = () => {
    if (product) {
      addToCart(product, quantity);
      setIsCartOpen(true);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 pt-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-muted-foreground">Loading product...</p>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Error state
  if (error || !product) {
    return (
      <div className="flex min-h-screen flex-col">
        <Header />
        <main className="flex-1 pt-16">
          <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-16 text-center">
            <h1 className="text-2xl font-bold mb-4">Product not found</h1>
            <p className="text-muted-foreground mb-8">The product you're looking for doesn't exist or has been removed.</p>
            <Link to="/products">
              <AnimatedButton>Return to Products</AnimatedButton>
            </Link>
          </div>
        </main>
        <Footer />
      </div>
    );
  }

  // Array of images for the product carousel
  // In a real app, each product would have multiple images
  const productImages = [product.image, ...Array(3).fill(product.image)];
  
  return (
    <div className="flex min-h-screen flex-col">
      <Header />
      <main className="flex-1 pt-16">
        <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8 py-8">
          {/* Breadcrumbs */}
          <nav className="mb-6">
            <ol className="flex text-sm">
              <li className="flex items-center">
                <Link to="/" className="text-muted-foreground hover:text-foreground">Home</Link>
                <ChevronRight className="h-4 w-4 mx-2 text-muted-foreground" />
              </li>
              <li className="flex items-center">
                <Link to="/products" className="text-muted-foreground hover:text-foreground">Products</Link>
                <ChevronRight className="h-4 w-4 mx-2 text-muted-foreground" />
              </li>
              <li className="flex items-center">
                <Link to={`/products?category=${product.category}`} className="text-muted-foreground hover:text-foreground">
                  {product.category.charAt(0).toUpperCase() + product.category.slice(1)}
                </Link>
                <ChevronRight className="h-4 w-4 mx-2 text-muted-foreground" />
              </li>
              <li className="text-foreground font-medium truncate max-w-[150px] sm:max-w-xs">
                {product.name}
              </li>
            </ol>
          </nav>
          
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
            {/* Product Images */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className="space-y-4"
            >
              <div className="relative aspect-square overflow-hidden rounded-xl bg-secondary">
                <motion.img
                  key={selectedImage}
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                  src={productImages[selectedImage]}
                  alt={product.name}
                  className="h-full w-full object-cover object-center"
                />
                
                {product.isNewProduct && (
                  <div className="absolute left-4 top-4 z-10 rounded-full bg-primary px-3 py-1 text-xs font-medium text-white">
                    New
                  </div>
                )}
                
                {product.isOnSale && (
                  <div className="absolute right-4 top-4 z-10 rounded-full bg-destructive px-3 py-1 text-xs font-medium text-white">
                    Sale
                  </div>
                )}
              </div>
              
              <div className="flex space-x-4 overflow-auto pb-2">
                {productImages.map((image, index) => (
                  <motion.button
                    key={index}
                    whileHover={{ scale: 1.05 }}
                    transition={{ duration: 0.3 }}
                    onClick={() => setSelectedImage(index)}
                    className={`relative aspect-square w-20 overflow-hidden rounded-md ${
                      selectedImage === index ? 'ring-2 ring-primary' : 'ring-1 ring-border'
                    }`}
                  >
                    <img
                      src={image}
                      alt={`${product.name} thumbnail ${index + 1}`}
                      className="h-full w-full object-cover object-center"
                    />
                  </motion.button>
                ))}
              </div>
            </motion.div>
            
            {/* Product Info */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
              className="flex flex-col"
            >
              <div>
                {product.rating && (
                  <div className="flex items-center mb-4">
                    <div className="flex items-center text-amber-500">
                      {[...Array(5)].map((_, i) => (
                        <svg 
                          key={i} 
                          xmlns="http://www.w3.org/2000/svg" 
                          width="18" 
                          height="18" 
                          viewBox="0 0 24 24" 
                          fill={i < Math.floor(product.rating) ? "currentColor" : "none"} 
                          stroke="currentColor" 
                          strokeWidth="2" 
                          strokeLinecap="round" 
                          strokeLinejoin="round"
                          className={i < Math.floor(product.rating) ? "" : "text-gray-300"}
                        >
                          <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
                        </svg>
                      ))}
                    </div>
                    <span className="ml-2 text-sm text-muted-foreground">
                      {product.rating} ({product.reviews} reviews)
                    </span>
                  </div>
                )}
                
                <h1 className="text-3xl font-bold text-foreground">{product.name}</h1>
                
                <div className="mt-4 flex items-center">
                  {product.isOnSale && product.salePrice ? (
                    <>
                      <p className="text-2xl font-bold text-foreground">${product.salePrice}</p>
                      <p className="ml-3 text-lg text-muted-foreground line-through">${product.price}</p>
                      <span className="ml-3 rounded-full bg-destructive/10 px-2.5 py-1 text-xs font-medium text-destructive">
                        Save ${(product.price - product.salePrice).toFixed(2)}
                      </span>
                    </>
                  ) : (
                    <p className="text-2xl font-bold text-foreground">${product.price}</p>
                  )}
                </div>
                
                <p className="mt-6 text-muted-foreground">{product.description}</p>
                
                <div className="mt-8 space-y-4 border-t border-border pt-6">
                  {/* Quantity selector */}
                  <div>
                    <label htmlFor="quantity" className="block text-sm font-medium text-foreground mb-2">
                      Quantity
                    </label>
                    <div className="flex w-36 items-center">
                      <button
                        type="button"
                        onClick={() => setQuantity(Math.max(1, quantity - 1))}
                        className="h-10 w-10 flex items-center justify-center rounded-l-md border border-r-0 border-input bg-secondary text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                      >
                        <Minus size={16} />
                      </button>
                      <input
                        type="number"
                        id="quantity"
                        value={quantity}
                        onChange={(e) => setQuantity(parseInt(e.target.value) || 1)}
                        min="1"
                        className="h-10 w-16 border-y border-input bg-transparent text-center [appearance:textfield] [&::-webkit-inner-spin-button]:appearance-none [&::-webkit-outer-spin-button]:appearance-none"
                      />
                      <button
                        type="button"
                        onClick={() => setQuantity(quantity + 1)}
                        className="h-10 w-10 flex items-center justify-center rounded-r-md border border-l-0 border-input bg-secondary text-muted-foreground hover:bg-accent hover:text-accent-foreground"
                      >
                        <Plus size={16} />
                      </button>
                    </div>
                  </div>
                  
                  {/* Availability */}
                  <div className="flex items-center">
                    <span className="text-sm font-medium text-foreground mr-2">Availability:</span>
                    {product.stock > 0 ? (
                      <span className="text-sm text-green-600">In Stock ({product.stock} available)</span>
                    ) : (
                      <span className="text-sm text-destructive">Out of Stock</span>
                    )}
                  </div>
                  
                  {/* Add to cart button */}
                  <div className="flex gap-4">
                    <AnimatedButton
                      className="flex-1"
                      size="lg"
                      disabled={product.stock === 0}
                      onClick={handleAddToCart}
                    >
                      <ShoppingCart size={20} />
                      Add to Cart
                    </AnimatedButton>

                    <WishlistButton
                      product={product}
                      variant="outline"
                      size="lg"
                      className="aspect-square p-0 h-11 w-11 flex items-center justify-center"
                    />

                    <AnimatedButton
                      variant="outline"
                      className="aspect-square p-0 h-11 w-11 flex items-center justify-center"
                      aria-label="Share product"
                    >
                      <Share2 size={20} />
                    </AnimatedButton>
                  </div>
                </div>
                
                {/* Shipping and returns */}
                <div className="mt-8 grid grid-cols-1 sm:grid-cols-3 gap-4">
                  <div className="flex items-start space-x-3">
                    <Truck className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <h4 className="text-sm font-medium">Free Shipping</h4>
                      <p className="text-xs text-muted-foreground">On orders over $50</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <ShieldCheck className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <h4 className="text-sm font-medium">2-Year Warranty</h4>
                      <p className="text-xs text-muted-foreground">Full coverage</p>
                    </div>
                  </div>
                  
                  <div className="flex items-start space-x-3">
                    <RefreshCw className="h-5 w-5 text-muted-foreground" />
                    <div>
                      <h4 className="text-sm font-medium">30-Day Returns</h4>
                      <p className="text-xs text-muted-foreground">Hassle-free returns</p>
                    </div>
                  </div>
                </div>
              </div>
              
              {/* Specifications */}
              {product.specifications && (
                <div className="mt-10">
                  <ProductSpecifications
                    specifications={product.specifications}
                    category={product.category}
                  />
                </div>
              )}
            </motion.div>
          </div>
        </div>
      </main>
      <Footer />
      <CartDrawer open={isCartOpen} onClose={() => setIsCartOpen(false)} />
    </div>
  );
};

export default ProductDetail;
