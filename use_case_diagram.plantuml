@startuml
left to right direction

actor User
actor Admin

rectangle Backend {
  User -- (Authentication)
  User -- (View Products)
  User -- (Add to Cart)
  User -- (Checkout)
  User -- (Contact Support)

  Admin -- (Manage Products)
  Admin -- (Manage Users)
  Admin -- (View Orders)
}

rectangle Frontend {
  User -- (Browse Products)
  User -- (Search Products)
  User -- (View Product Details)
  User -- (Manage Profile)
}

User -- (Repair Request)

@enduml