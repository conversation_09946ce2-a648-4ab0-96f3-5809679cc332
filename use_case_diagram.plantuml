@startuml
left to right direction

actor User
actor Admin

rectangle Backend {
  User -- (Authentication)
  User -- (View Products)
  User -- (Add to Cart)
  User -- (Checkout)
  User -- (Contact Support)
  User -- (Manage Personal Wishlist)

  Admin -- (Manage Products)
  Admin -- (Manage Users)
  Admin -- (View Orders)
  Admin -- (Manage Repair Requests)
}

rectangle Frontend {
  User -- (Browse Products)
  User -- (Search Products)
  User -- (View Product Details)
  User -- (Manage Profile)
  User -- (View Personal Wishlist)
  User -- (Add/Remove from Wishlist)
}

User -- (Submit Repair Request)
User -- (Track Repair Status)

@enduml