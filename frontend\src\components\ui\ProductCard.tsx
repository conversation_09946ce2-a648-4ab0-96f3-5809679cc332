
import React from 'react';
import { Link } from 'react-router-dom';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { AnimatedButton } from './AnimatedButton';
import { WishlistButton } from './WishlistButton';
import { Product } from '@/services/productService';
import { useCart } from '@/contexts/CartContext';
import { useCompare } from '@/contexts/CompareContext';
import { GitCompare, Check } from 'lucide-react';
import { Toggle } from './toggle';

interface ProductCardProps {
  product: Product;
  className?: string;
  index?: number;
}

export function ProductCard({ product, className, index = 0 }: ProductCardProps) {
  const delay = index * 0.1;
  const { addToCart } = useCart();
  const { addToCompare, isInCompare, removeFromCompare } = useCompare();
  
  const handleAddToCart = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    addToCart(product, 1);
  };

  const handleCompareToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    
    if (isInCompare(product._id)) {
      removeFromCompare(product._id);
    } else {
      addToCompare(product);
    }
  };
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      className={cn(
        'group relative overflow-hidden rounded-xl bg-white shadow-sm transition-all duration-300 hover:shadow-md',
        className
      )}
    >
      <Link to={`/products/${product._id}`} className="block">
        <div className="relative aspect-square overflow-hidden">
          <motion.div
            whileHover={{ scale: 1.05 }}
            transition={{ duration: 0.6, ease: [0.22, 1, 0.36, 1] }}
            className="h-full w-full"
          >
            <img
              src={product.image}
              alt={product.name}
              className="h-full w-full object-cover transition-transform"
              loading="lazy"
            />
          </motion.div>
          
          {product.isNewProduct && (
            <div className="absolute left-4 top-4 z-10 rounded-full bg-primary px-3 py-1 text-xs font-medium text-white">
              New
            </div>
          )}
          
          {product.isOnSale && (
            <div className="absolute left-4 top-14 z-10 rounded-full bg-destructive px-3 py-1 text-xs font-medium text-white">
              Sale
            </div>
          )}
        </div>
        
        <div className="p-4">
          <div className="mb-1 flex items-center justify-between">
            <h3 className="font-medium text-foreground">{product.name}</h3>
            <div className="flex items-center">
              {product.rating && (
                <div className="flex items-center text-amber-500">
                  <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="currentColor" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2" />
                  </svg>
                  <span className="ml-1 text-xs">{product.rating}</span>
                </div>
              )}
            </div>
          </div>
          
          <p className="mb-3 text-sm text-muted-foreground line-clamp-2">
            {product.description}
          </p>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              {product.isOnSale && product.salePrice ? (
                <>
                  <span className="text-lg font-semibold">${product.salePrice}</span>
                  <span className="text-sm text-muted-foreground line-through">${product.price}</span>
                </>
              ) : (
                <span className="text-lg font-semibold">${product.price}</span>
              )}
            </div>
          </div>
        </div>
      </Link>
      
      {/* Action buttons - Positioned outside the Link to prevent navigation */}
      <div className="absolute right-3 top-3 z-20 flex flex-col gap-2">
        {/* Wishlist button */}
        <WishlistButton
          product={product}
          className="h-8 w-8 rounded-full bg-white/90 shadow-md hover:bg-white"
        />

        {/* Compare toggle button */}
        <Toggle
          pressed={isInCompare(product._id)}
          onPressedChange={() => {
            if (isInCompare(product._id)) {
              removeFromCompare(product._id);
            } else {
              addToCompare(product);
            }
          }}
          aria-label={isInCompare(product._id) ? "Remove from compare" : "Add to compare"}
          className="h-8 w-8 rounded-full bg-white/90 p-0 shadow-md hover:bg-white"
          onClick={handleCompareToggle}
        >
          {isInCompare(product._id) ? (
            <Check size={16} className="text-primary" />
          ) : (
            <GitCompare size={16} />
          )}
        </Toggle>
      </div>
      
      <div className="absolute bottom-0 left-0 right-0 translate-y-full bg-white p-4 transition-transform duration-300 ease-in-out group-hover:translate-y-0">
        <AnimatedButton 
          className="w-full"
          onClick={handleAddToCart}
        >
          Add to Cart
        </AnimatedButton>
      </div>
    </motion.div>
  );
}

export default ProductCard;
