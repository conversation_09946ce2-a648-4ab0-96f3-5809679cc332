# TechStore - Diagramme de Cas d'Usage (Use Case Diagram)

## 📋 Vue d'ensemble

Ce document décrit le diagramme de cas d'usage pour le projet TechStore, une application e-commerce complète avec des fonctionnalités avancées de gestion de produits, de commandes, de réparation et de web crawling.

## 👥 Acteurs du Système

### Acteurs Principaux

1. **👥 Visiteur (Guest)**
   - Utilisateur non authentifié
   - Peut consulter les produits et s'inscrire
   - Accès limité aux fonctionnalités publiques

2. **👤 Client/Customer**
   - Utilisateur authentifié avec rôle "Customer"
   - Peut effectuer des achats et gérer son profil
   - Accès aux fonctionnalités de commerce électronique

3. **👨‍💻 Manager**
   - Utilisateur avec privilèges de gestion
   - Peut gérer les commandes et consulter les rapports
   - Rôle intermédiaire entre Customer et Admin

4. **👨‍💼 Administrateur (Admin)**
   - Utilisateur avec tous les privilèges
   - Peut gérer tous les aspects du système
   - Accès complet aux fonctionnalités d'administration

### Acteurs Secondaires (Systèmes Externes)

5. **📧 Système Email**
   - Service d'envoi d'emails automatisés
   - Notifications et confirmations

6. **💳 Système de Paiement**
   - Intégration Stripe pour les paiements
   - Traitement sécurisé des transactions

7. **🕷️ Web Crawler**
   - Service de crawling automatisé
   - Extraction de données de sites web

## 🎯 Cas d'Usage par Catégorie

### 🔐 Authentification
- **UC1**: S'inscrire - Création d'un nouveau compte utilisateur
- **UC2**: Se connecter - Authentification avec email/mot de passe
- **UC3**: Se déconnecter - Terminer la session utilisateur
- **UC4**: Gérer profil - Modifier les informations personnelles

### 📦 Gestion des Produits
- **UC5**: Consulter produits - Afficher la liste des produits
- **UC6**: Rechercher produits - Recherche par mots-clés
- **UC7**: Filtrer produits - Filtrage par catégorie, prix, etc.
- **UC8**: Voir détails produit - Affichage détaillé d'un produit
- **UC9**: Ajouter produit - Création d'un nouveau produit (Admin)
- **UC10**: Modifier produit - Édition des informations produit (Admin)
- **UC11**: Supprimer produit - Suppression d'un produit (Admin)
- **UC12**: Gérer stock - Gestion des quantités en stock (Admin)
- **UC13**: Gérer spécifications - Gestion des caractéristiques techniques (Admin)

### 🛒 Panier & Commandes
- **UC14**: Ajouter au panier - Ajout d'un produit au panier
- **UC15**: Modifier panier - Modification des quantités
- **UC16**: Supprimer du panier - Retrait d'un produit du panier
- **UC17**: Passer commande - Processus de commande
- **UC18**: Effectuer paiement - Traitement du paiement
- **UC19**: Consulter commandes - Historique des commandes
- **UC20**: Suivre commande - Suivi du statut de livraison
- **UC21**: Annuler commande - Annulation d'une commande

### 📋 Gestion Commandes Admin
- **UC22**: Voir toutes commandes - Vue d'ensemble des commandes
- **UC23**: Modifier statut commande - Mise à jour du statut
- **UC24**: Gérer livraisons - Gestion des expéditions
- **UC25**: Générer rapports - Création de rapports de vente

### 👥 Gestion Utilisateurs
- **UC26**: Voir tous utilisateurs - Liste des utilisateurs
- **UC27**: Modifier rôles utilisateurs - Attribution des rôles
- **UC28**: Activer/Désactiver utilisateurs - Gestion du statut
- **UC29**: Consulter statistiques - Statistiques utilisateurs

### 🔧 Services de Réparation
- **UC30**: Demander réparation - Soumission d'une demande
- **UC31**: Consulter services réparation - Information sur les services
- **UC32**: Suivre demande réparation - Suivi du statut
- **UC33**: Gérer demandes réparation - Administration des demandes

### 🕷️ Web Crawler
- **UC34**: Crawler site web - Lancement du crawling
- **UC35**: Extraire données produits - Extraction automatisée
- **UC36**: Configurer crawler - Paramétrage du crawler
- **UC37**: Consulter statut crawler - Monitoring du service

### 📞 Contact & Support
- **UC38**: Envoyer message contact - Formulaire de contact
- **UC39**: Consulter page À propos - Informations sur l'entreprise
- **UC40**: Recevoir notifications email - Emails automatisés

### ❤️ Liste de Souhaits & Comparaison
- **UC41**: Ajouter à la wishlist - Sauvegarde de produits favoris
- **UC42**: Gérer wishlist - Gestion de la liste de souhaits
- **UC43**: Comparer produits - Comparaison de caractéristiques

### 📊 Tableau de Bord Admin
- **UC44**: Consulter dashboard - Vue d'ensemble administrative
- **UC45**: Voir analytics - Analyses et métriques
- **UC46**: Configurer système - Paramètres système
- **UC47**: Gérer paramètres - Configuration générale

## 🔗 Relations entre Cas d'Usage

### Relations d'Inclusion (include)
- **UC17** (Passer commande) inclut **UC18** (Effectuer paiement)
- **UC30** (Demander réparation) inclut **UC40** (Recevoir notifications email)
- **UC1** (S'inscrire) inclut **UC40** (Recevoir notifications email)

### Relations d'Extension (extend)
- **UC8** (Voir détails produit) peut être étendu par **UC41** (Ajouter à la wishlist)
- **UC8** (Voir détails produit) peut être étendu par **UC43** (Comparer produits)
- **UC5** (Consulter produits) peut être étendu par **UC6** (Rechercher produits)
- **UC5** (Consulter produits) peut être étendu par **UC7** (Filtrer produits)

## 🛡️ Matrice des Permissions

| Cas d'Usage | Visiteur | Client | Manager | Admin |
|-------------|----------|--------|---------|-------|
| Consulter produits | ✅ | ✅ | ✅ | ✅ |
| S'inscrire | ✅ | ❌ | ❌ | ❌ |
| Passer commande | ❌ | ✅ | ✅ | ✅ |
| Gérer produits | ❌ | ❌ | ❌ | ✅ |
| Gérer utilisateurs | ❌ | ❌ | ❌ | ✅ |
| Voir rapports | ❌ | ❌ | ✅ | ✅ |
| Web Crawler | ❌ | ❌ | ❌ | ✅ |

## 🔧 Technologies Utilisées

- **Frontend**: React 18, TypeScript, Tailwind CSS
- **Backend**: Express.js, Node.js
- **Base de données**: MongoDB avec Mongoose
- **Authentification**: JWT (JSON Web Tokens)
- **Paiement**: Stripe API
- **Email**: Nodemailer
- **Web Crawling**: Puppeteer, Cheerio

## 📝 Notes d'Implémentation

1. **Sécurité**: Tous les cas d'usage sensibles nécessitent une authentification
2. **Validation**: Validation côté client et serveur pour tous les formulaires
3. **Notifications**: Système d'email automatisé pour les événements importants
4. **Responsive**: Interface adaptée à tous les types d'appareils
5. **Performance**: Optimisation des requêtes et mise en cache appropriée
