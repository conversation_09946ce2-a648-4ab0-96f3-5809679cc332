
import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import { Layout } from '@/components/layout/Layout';
import { useAuth } from '@/contexts/AuthContext';
import { useWishlist } from '@/contexts/WishlistContext';
import { useCart } from '@/contexts/CartContext';
import { useToast } from '@/hooks/use-toast';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Separator } from '@/components/ui/separator';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { Info, Heart, ShoppingCart, Trash2 } from 'lucide-react';

const Profile = () => {
  const { user, logout } = useAuth();
  const { toast } = useToast();
  const [searchParams] = useSearchParams();
  const [name, setName] = useState(user?.name || '');
  const [email, setEmail] = useState(user?.email || '');
  const [isSaving, setIsSaving] = useState(false);
  const [activeTab, setActiveTab] = useState('profile');

  // Handle URL tab parameter
  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && ['profile', 'orders', 'wishlist'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Success message
    toast({
      title: "Profile updated",
      description: "Your profile information has been updated successfully.",
    });
    
    setIsSaving(false);
  };

  if (!user) {
    return (
      <Layout>
        <div className="container py-20">
          <Alert>
            <Info className="h-4 w-4" />
            <AlertTitle>Authentication required</AlertTitle>
            <AlertDescription>
              Please login to view your profile.
            </AlertDescription>
          </Alert>
        </div>
      </Layout>
    );
  }

  return (
    <Layout>
      <div className="container py-10">
        <div className="flex flex-col md:flex-row md:items-start gap-8">
          <div className="w-full md:w-1/4">
            <Card>
              <CardContent className="pt-6">
                <div className="flex flex-col items-center">
                  <Avatar className="h-24 w-24 mb-4">
                    <AvatarImage src={`https://api.dicebear.com/7.x/initials/svg?seed=${user.name}`} alt={user.name} />
                    <AvatarFallback>{user.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                  </Avatar>
                  <h2 className="text-xl font-bold">{user.name}</h2>
                  <p className="text-sm text-muted-foreground mb-2">{user.email}</p>
                  <Badge variant="outline" className="mt-2">Customer</Badge>
                </div>
                <Separator className="my-4" />
                <nav className="flex flex-col space-y-1">
                  <Button variant="ghost" className="justify-start">Profile Information</Button>
                  <Button variant="ghost" className="justify-start">Order History</Button>
                  <Button variant="ghost" className="justify-start">Wishlist</Button>
                  <Button variant="ghost" className="justify-start">Payment Methods</Button>
                  <Button variant="ghost" className="justify-start">Addresses</Button>
                  <Button variant="ghost" className="justify-start text-destructive hover:text-destructive/90" onClick={logout}>Logout</Button>
                </nav>
              </CardContent>
            </Card>
          </div>
          
          <div className="flex-1">
            <Tabs value={activeTab} onValueChange={setActiveTab}>
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="profile">Profile</TabsTrigger>
                <TabsTrigger value="orders">Orders</TabsTrigger>
                <TabsTrigger value="wishlist">Wishlist</TabsTrigger>
              </TabsList>
              
              <TabsContent value="profile" className="mt-6">
                <Card>
                  <CardHeader>
                    <CardTitle>Profile Information</CardTitle>
                    <CardDescription>Update your account information here.</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <form onSubmit={handleUpdateProfile}>
                      <div className="grid gap-4">
                        <div className="grid gap-2">
                          <label htmlFor="name" className="text-sm font-medium">Name</label>
                          <Input 
                            id="name" 
                            value={name} 
                            onChange={(e) => setName(e.target.value)} 
                            className="w-full"
                          />
                        </div>
                        <div className="grid gap-2">
                          <label htmlFor="email" className="text-sm font-medium">Email</label>
                          <Input 
                            id="email" 
                            type="email" 
                            value={email} 
                            onChange={(e) => setEmail(e.target.value)} 
                            className="w-full"
                          />
                        </div>
                      </div>
                    </form>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button variant="outline">Cancel</Button>
                    <Button 
                      onClick={handleUpdateProfile} 
                      disabled={isSaving}
                    >
                      {isSaving ? 'Saving...' : 'Save Changes'}
                    </Button>
                  </CardFooter>
                </Card>
              </TabsContent>
              
              <TabsContent value="orders" className="mt-6">
                <OrderHistory />
              </TabsContent>
              
              <TabsContent value="wishlist" className="mt-6">
                <WishlistItems />
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </Layout>
  );
};

// Order History Component
const OrderHistory = () => {
  const orders = [
    { id: 'ORD-1234', date: '2023-06-15', status: 'Delivered', total: 125.99 },
    { id: 'ORD-5678', date: '2023-05-30', status: 'Processing', total: 79.99 },
    { id: 'ORD-9012', date: '2023-04-22', status: 'Cancelled', total: 49.99 },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Order History</CardTitle>
        <CardDescription>View your previous orders and their status.</CardDescription>
      </CardHeader>
      <CardContent>
        {orders.length === 0 ? (
          <p className="text-center py-8 text-muted-foreground">You haven't placed any orders yet.</p>
        ) : (
          <div className="space-y-6">
            {orders.map((order) => (
              <div key={order.id} className="border rounded-lg p-4">
                <div className="flex justify-between items-center mb-2">
                  <div className="font-medium">{order.id}</div>
                  <Badge 
                    variant={order.status === 'Delivered' ? 'default' : 
                             order.status === 'Processing' ? 'secondary' : 'destructive'}>
                    {order.status}
                  </Badge>
                </div>
                <div className="text-sm text-muted-foreground mb-2">Ordered on {new Date(order.date).toLocaleDateString()}</div>
                <div className="flex justify-between items-center">
                  <div className="text-sm">Total: ${order.total.toFixed(2)}</div>
                  <Button variant="ghost" size="sm">View Details</Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// Wishlist Component
const WishlistItems = () => {
  const { wishlistItems, removeFromWishlist, clearWishlist, loading } = useWishlist();
  const { addToCart } = useCart();
  const { toast } = useToast();

  const handleAddToCart = (product: any) => {
    addToCart(product, 1);
  };

  const handleRemoveFromWishlist = async (productId: string) => {
    try {
      await removeFromWishlist(productId);
    } catch (error) {
      console.error('Error removing from wishlist:', error);
    }
  };

  const handleClearWishlist = async () => {
    if (wishlistItems.length > 0) {
      try {
        await clearWishlist();
      } catch (error) {
        console.error('Error clearing wishlist:', error);
      }
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Heart className="h-5 w-5 text-destructive" />
              Your Wishlist
              {wishlistItems.length > 0 && (
                <Badge variant="secondary">{wishlistItems.length}</Badge>
              )}
            </CardTitle>
            <CardDescription>Items you've saved for later.</CardDescription>
          </div>
          {wishlistItems.length > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={handleClearWishlist}
              className="text-destructive hover:text-destructive/90"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Clear All
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent>
        {wishlistItems.length === 0 ? (
          <div className="text-center py-12">
            <Heart className="h-16 w-16 mx-auto text-muted-foreground/50 mb-4" />
            <h3 className="text-lg font-medium text-muted-foreground mb-2">Your wishlist is empty</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Start adding products to your wishlist by clicking the heart icon on any product.
            </p>
            <Button variant="outline" onClick={() => window.location.href = '/products'}>
              Browse Products
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            {wishlistItems.map((item) => (
              <div key={item._id} className="flex items-center gap-4 border-b pb-4 last:border-b-0">
                <div className="h-20 w-20 rounded-lg bg-secondary overflow-hidden flex-shrink-0 relative">
                  <img
                    src={item.image}
                    alt={item.name}
                    className="h-full w-full object-cover"
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = '/placeholder.svg';
                    }}
                  />
                  {item.isOnSale && (
                    <div className="absolute top-1 right-1 bg-destructive text-destructive-foreground text-xs font-bold px-1.5 py-0.5 rounded">
                      SALE
                    </div>
                  )}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-sm line-clamp-2 mb-1">{item.name}</h4>
                  <p className="text-xs text-muted-foreground mb-2 line-clamp-2">{item.description}</p>
                  <div className="flex items-center gap-2">
                    {item.isOnSale && item.salePrice ? (
                      <>
                        <span className="text-lg font-bold text-destructive">
                          ${item.salePrice.toFixed(2)}
                        </span>
                        <span className="text-sm text-muted-foreground line-through">
                          ${item.price.toFixed(2)}
                        </span>
                      </>
                    ) : (
                      <span className="text-lg font-bold">
                        ${item.price.toFixed(2)}
                      </span>
                    )}
                    <Badge variant="outline" className="text-xs">
                      {item.category}
                    </Badge>
                  </div>
                  {item.stock <= 5 && item.stock > 0 && (
                    <p className="text-xs text-orange-600 mt-1">
                      Only {item.stock} left in stock
                    </p>
                  )}
                  {item.stock === 0 && (
                    <p className="text-xs text-destructive mt-1">Out of stock</p>
                  )}
                </div>
                <div className="flex flex-col gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleAddToCart(item)}
                    disabled={item.stock === 0}
                    className="w-full"
                  >
                    <ShoppingCart className="h-4 w-4 mr-2" />
                    {item.stock === 0 ? 'Out of Stock' : 'Add to Cart'}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemoveFromWishlist(item._id)}
                    className="text-destructive hover:text-destructive/90 w-full"
                  >
                    <Trash2 className="h-4 w-4 mr-2" />
                    Remove
                  </Button>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
      {wishlistItems.length > 0 && (
        <CardFooter className="bg-muted/50">
          <div className="flex items-center justify-between w-full text-sm text-muted-foreground">
            <span>{wishlistItems.length} item{wishlistItems.length !== 1 ? 's' : ''} in your wishlist</span>
            <span>
              Total value: $
              {wishlistItems.reduce((total, item) => {
                const price = item.isOnSale && item.salePrice ? item.salePrice : item.price;
                return total + price;
              }, 0).toFixed(2)}
            </span>
          </div>
        </CardFooter>
      )}
    </Card>
  );
};

export default Profile;
