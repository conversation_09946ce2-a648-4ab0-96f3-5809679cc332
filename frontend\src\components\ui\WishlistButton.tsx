
import React, { useState } from 'react';
import { Heart } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useWishlist } from '@/contexts/WishlistContext';
import { Product } from '@/services/productService';
import { cn } from '@/lib/utils';

interface WishlistButtonProps {
  product: Product;
  variant?: 'default' | 'outline' | 'ghost';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
}

export const WishlistButton: React.FC<WishlistButtonProps> = ({
  product,
  variant = 'ghost',
  size = 'icon',
  className,
}) => {
  const { isInWishlist, addToWishlist, removeFromWishlist, loading } = useWishlist();
  const [isToggling, setIsToggling] = useState(false);
  const isActive = isInWishlist(product._id);

  const toggleWishlist = async () => {
    if (isToggling) return; // Prevent multiple clicks

    setIsToggling(true);
    try {
      if (isActive) {
        await removeFromWishlist(product._id);
      } else {
        await addToWishlist(product);
      }
    } finally {
      setIsToggling(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={toggleWishlist}
      disabled={isToggling || loading}
      className={cn(
        className,
        isActive && "text-destructive hover:text-destructive/90",
        (isToggling || loading) && "opacity-50 cursor-not-allowed"
      )}
      aria-label={isActive ? "Remove from wishlist" : "Add to wishlist"}
    >
      <Heart className={cn(
        "h-5 w-5",
        isActive && "fill-current"
      )} />
    </Button>
  );
};
