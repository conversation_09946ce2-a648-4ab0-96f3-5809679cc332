# TechStore - Documentation des Cas d'Usage

## 📋 Vue d'ensemble

Ce document décrit les cas d'usage du système TechStore, une plateforme e-commerce spécialisée dans la vente de matériel informatique avec des fonctionnalités avancées de gestion des spécifications produits et services de réparation.

## 👥 Acteurs du Système

### Acteurs Principaux

| Acteur | Description | Permissions |
|--------|-------------|-------------|
| **👥 Visiteur** | Utilisateur non authentifié | Consultation publique uniquement |
| **👤 Client** | Utilisateur authentifié | E-commerce + services clients |
| **👨‍💻 Manager** | Gestionnaire avec privilèges intermédiaires | Gestion produits + commandes |
| **👨‍💼 Administrateur** | Accès complet au système | Toutes les fonctionnalités |

### Systèmes Externes

| Système | Description | Intégration |
|---------|-------------|-------------|
| **📧 Système Email** | Service de notifications automatisées | Nodemailer + Gmail |
| **💳 Système Paiement** | Traitement des paiements | Stripe API |
| **🕷️ Web Crawler** | Service d'extraction de données | Puppeteer |

## 🎯 Cas d'Usage par Domaine Fonctionnel

### 1. 🔐 Authentification & Profil

#### UC_Register - S'inscrire
- **Acteur**: Visiteur
- **Description**: Création d'un nouveau compte utilisateur
- **Préconditions**: Aucune
- **Postconditions**: Compte créé, email de confirmation envoyé
- **Flux principal**:
  1. Saisir informations (nom, email, mot de passe)
  2. Valider les données
  3. Créer le compte avec rôle "Customer"
  4. Envoyer email de confirmation
- **Extensions**: Validation des données, gestion des erreurs

#### UC_Login - Se connecter
- **Acteur**: Visiteur
- **Description**: Authentification dans le système
- **Préconditions**: Compte existant
- **Postconditions**: Session utilisateur créée, JWT généré
- **Flux principal**:
  1. Saisir email/mot de passe
  2. Valider les identifiants
  3. Générer token JWT
  4. Rediriger selon le rôle

### 2. 📦 Gestion des Produits

#### UC_ViewCatalog - Consulter catalogue
- **Acteur**: Tous
- **Description**: Affichage de la liste des produits
- **Préconditions**: Aucune
- **Postconditions**: Liste des produits affichée
- **Implémentation**: API GET /api/products

#### UC_ManageSpecs - Gérer spécifications
- **Acteur**: Admin, Manager
- **Description**: Configuration des spécifications par catégorie
- **Préconditions**: Authentification admin/manager
- **Postconditions**: Spécifications mises à jour
- **Spécifications par catégorie**:
  - **Laptops**: processor, memory, storage, display, graphics, battery
  - **Desktops**: processor, memory, storage, graphics, cooling, motherboard
  - **Components**: type, model, specifications, power, compatibility
  - **Accessories**: type, compatibility, connectivity, battery, features

### 3. 🛒 E-commerce

#### UC_Checkout - Passer commande
- **Acteur**: Client
- **Description**: Processus de commande complet
- **Préconditions**: Panier non vide, authentification
- **Postconditions**: Commande créée, paiement traité
- **Relations**: Include UC_Payment, UC_ConfirmOrder
- **Flux principal**:
  1. Réviser le panier
  2. Saisir informations de livraison
  3. Choisir mode de paiement
  4. Traiter le paiement (Stripe)
  5. Confirmer la commande
  6. Envoyer email de confirmation

### 4. 👨‍💼 Administration

#### UC_Dashboard - Tableau de bord
- **Acteur**: Admin
- **Description**: Vue d'ensemble des métriques système
- **Préconditions**: Authentification admin
- **Postconditions**: Métriques affichées
- **Fonctionnalités**:
  - Statistiques de vente
  - Nombre d'utilisateurs
  - Produits populaires
  - Commandes récentes
  - Analytics en temps réel

#### UC_ManageUsers - Gérer utilisateurs
- **Acteur**: Admin
- **Description**: Administration des comptes utilisateurs
- **Préconditions**: Authentification admin
- **Postconditions**: Utilisateurs mis à jour
- **Actions possibles**:
  - Voir liste des utilisateurs
  - Modifier les rôles
  - Activer/désactiver comptes
  - Consulter l'historique

### 5. 🔧 Services Spécialisés

#### UC_RepairRequest - Demander réparation
- **Acteur**: Client
- **Description**: Soumission d'une demande de réparation
- **Préconditions**: Authentification client
- **Postconditions**: Demande créée, email envoyé
- **Types de réparation**:
  - Hardware: Écran, batterie, clavier, carte mère
  - Software: OS, drivers, optimisation
  - Security: Antivirus, récupération données
  - Networking: WiFi, configuration réseau

#### UC_RunCrawler - Lancer crawling
- **Acteur**: Admin
- **Description**: Extraction automatisée de données produits
- **Préconditions**: Authentification admin, configuration crawler
- **Postconditions**: Données extraites et stockées
- **Technologies**: Puppeteer, MongoDB
- **Sources**: Sites e-commerce partenaires

## 🔗 Relations entre Cas d'Usage

### Relations Include
- `UC_Checkout` include `UC_Payment`
- `UC_AddProduct` include `UC_ManageSpecs`
- `UC_Register` include `EmailSystem`
- `UC_RepairRequest` include `EmailSystem`

### Relations Extend
- `UC_ManageSpecs` extend `UC_ViewSpecs`
- `UC_ViewProductDetails` extend `UC_AddToCart`
- `UC_ManageCart` extend `UC_Checkout`
- `UC_Login` extend `UC_ManageProfile`

### Héritage d'Acteurs
- `Customer` hérite de `Guest` (accès aux fonctionnalités publiques + privées)

## 🛠️ Implémentation Technique

### Backend (Node.js + Express)
- **Routes**: `/api/products`, `/api/auth`, `/api/orders`, `/api/repair`
- **Middleware**: Authentication, Authorization, CORS
- **Base de données**: MongoDB avec Mongoose
- **Services**: Email (Nodemailer), Payment (Stripe), Crawler (Puppeteer)

### Frontend (React + TypeScript)
- **Pages**: Home, Products, Admin, Profile, Checkout
- **Composants**: ProductCard, AdminLayout, SpecificationForm
- **Services**: API calls, Authentication, State management
- **UI**: Tailwind CSS, Radix UI, Sonner (toasts)

### Sécurité
- **Authentication**: JWT tokens
- **Authorization**: Role-based access control
- **Validation**: Input validation, XSS protection
- **HTTPS**: SSL/TLS encryption

## 📊 Métriques et KPIs

### Métriques Utilisateur
- Nombre d'inscriptions
- Taux de conversion visiteur → client
- Panier moyen
- Taux d'abandon de panier

### Métriques Produit
- Produits les plus consultés
- Catégories populaires
- Stock en temps réel
- Spécifications les plus recherchées

### Métriques Système
- Performance API
- Temps de réponse
- Taux d'erreur
- Utilisation des ressources

## 🚀 Évolutions Futures

### Fonctionnalités Prévues
- **IA/ML**: Recommandations personnalisées
- **Mobile**: Application mobile native
- **B2B**: Fonctionnalités entreprise
- **Multi-langue**: Internationalisation
- **API**: API publique pour partenaires

### Améliorations Techniques
- **Performance**: Cache Redis, CDN
- **Scalabilité**: Microservices, Load balancing
- **Monitoring**: Logs centralisés, Alertes
- **Tests**: Couverture de tests automatisés
