@startuml TechStore_Use_Case_Diagram

!theme plain
skinparam backgroundColor #FFFFFF
skinparam actor {
    BackgroundColor #E3F2FD
    BorderColor #1976D2
    FontColor #1976D2
}
skinparam usecase {
    BackgroundColor #F3E5F5
    BorderColor #7B1FA2
    FontColor #4A148C
}
skinparam rectangle {
    BackgroundColor #FFF8E1
    BorderColor #F57C00
    FontColor #E65100
}

title TechStore - Diagramme de Cas d'Usage

' Acteurs principaux
actor "👥 Visiteur" as Guest
actor "👤 Client" as Customer
actor "👨‍💻 Manager" as Manager
actor "👨‍💼 Administrateur" as Admin

' Systèmes externes
actor "📧 Système Email" as EmailSystem
actor "💳 Système Paiement" as PaymentSystem
actor "🕷️ Web Crawler" as WebCrawler

' Système principal
rectangle "TechStore System" {
    
    ' === AUTHENTIFICATION & PROFIL ===
    package "Authentification & Profil" {
        usecase "S'inscrire" as UC_Register
        usecase "Se connecter" as UC_Login
        usecase "Se déconnecter" as UC_Logout
        usecase "Gérer profil" as UC_ManageProfile
        usecase "Réinitialiser mot de passe" as UC_ResetPassword
    }
    
    ' === GESTION DES PRODUITS ===
    package "Gestion des Produits" {
        usecase "Consulter catalogue" as UC_ViewCatalog
        usecase "Rechercher produits" as UC_SearchProducts
        usecase "Filtrer par catégorie" as UC_FilterCategory
        usecase "Voir détails produit" as UC_ViewProductDetails
        usecase "Consulter spécifications" as UC_ViewSpecs
        usecase "Ajouter produit" as UC_AddProduct
        usecase "Modifier produit" as UC_EditProduct
        usecase "Supprimer produit" as UC_DeleteProduct
        usecase "Gérer spécifications" as UC_ManageSpecs
        usecase "Gérer stock" as UC_ManageStock
    }
    
    ' === E-COMMERCE ===
    package "E-commerce" {
        usecase "Ajouter au panier" as UC_AddToCart
        usecase "Gérer panier" as UC_ManageCart
        usecase "Passer commande" as UC_Checkout
        usecase "Effectuer paiement" as UC_Payment
        usecase "Confirmer commande" as UC_ConfirmOrder
        usecase "Suivre commande" as UC_TrackOrder
        usecase "Consulter historique" as UC_OrderHistory
        usecase "Gérer commandes" as UC_ManageOrders
    }
    
    ' === ADMINISTRATION ===
    package "Administration" {
        usecase "Tableau de bord" as UC_Dashboard
        usecase "Gérer utilisateurs" as UC_ManageUsers
        usecase "Gérer rôles" as UC_ManageRoles
        usecase "Consulter analytics" as UC_ViewAnalytics
        usecase "Gérer paramètres" as UC_ManageSettings
        usecase "Exporter données" as UC_ExportData
    }
    
    ' === SERVICES SPÉCIALISÉS ===
    package "Services Spécialisés" {
        usecase "Demander réparation" as UC_RepairRequest
        usecase "Suivre réparation" as UC_TrackRepair
        usecase "Configurer crawler" as UC_ConfigureCrawler
        usecase "Lancer crawling" as UC_RunCrawler
        usecase "Consulter données crawlées" as UC_ViewCrawledData
    }
    
    ' === SUPPORT & CONTACT ===
    package "Support & Contact" {
        usecase "Contacter support" as UC_ContactSupport
        usecase "Consulter FAQ" as UC_ViewFAQ
        usecase "Soumettre feedback" as UC_SubmitFeedback
        usecase "Gérer tickets support" as UC_ManageTickets
    }
    
    ' === FONCTIONNALITÉS AVANCÉES ===
    package "Fonctionnalités Avancées" {
        usecase "Gérer liste souhaits" as UC_ManageWishlist
        usecase "Comparer produits" as UC_CompareProducts
        usecase "Recevoir notifications" as UC_ReceiveNotifications
        usecase "Consulter recommandations" as UC_ViewRecommendations
    }
}

' === RELATIONS VISITEUR ===
Guest --> UC_ViewCatalog
Guest --> UC_SearchProducts
Guest --> UC_FilterCategory
Guest --> UC_ViewProductDetails
Guest --> UC_ViewSpecs
Guest --> UC_Register
Guest --> UC_Login
Guest --> UC_ContactSupport
Guest --> UC_ViewFAQ

' === RELATIONS CLIENT ===
Customer --> UC_ViewCatalog
Customer --> UC_SearchProducts
Customer --> UC_FilterCategory
Customer --> UC_ViewProductDetails
Customer --> UC_ViewSpecs
Customer --> UC_Logout
Customer --> UC_ManageProfile
Customer --> UC_ResetPassword
Customer --> UC_AddToCart
Customer --> UC_ManageCart
Customer --> UC_Checkout
Customer --> UC_TrackOrder
Customer --> UC_OrderHistory
Customer --> UC_RepairRequest
Customer --> UC_TrackRepair
Customer --> UC_ContactSupport
Customer --> UC_SubmitFeedback
Customer --> UC_ManageWishlist
Customer --> UC_CompareProducts
Customer --> UC_ReceiveNotifications
Customer --> UC_ViewRecommendations

' === RELATIONS MANAGER ===
Manager --> UC_ViewCatalog
Manager --> UC_SearchProducts
Manager --> UC_AddProduct
Manager --> UC_EditProduct
Manager --> UC_ManageSpecs
Manager --> UC_ManageStock
Manager --> UC_ManageOrders
Manager --> UC_ViewAnalytics
Manager --> UC_ManageTickets
Manager --> UC_ViewCrawledData

' === RELATIONS ADMINISTRATEUR ===
Admin --> UC_Dashboard
Admin --> UC_ManageUsers
Admin --> UC_ManageRoles
Admin --> UC_AddProduct
Admin --> UC_EditProduct
Admin --> UC_DeleteProduct
Admin --> UC_ManageSpecs
Admin --> UC_ManageStock
Admin --> UC_ManageOrders
Admin --> UC_ViewAnalytics
Admin --> UC_ManageSettings
Admin --> UC_ExportData
Admin --> UC_ConfigureCrawler
Admin --> UC_RunCrawler
Admin --> UC_ViewCrawledData
Admin --> UC_ManageTickets

' === RELATIONS SYSTÈMES EXTERNES ===
UC_Payment --> PaymentSystem
UC_Register --> EmailSystem
UC_ResetPassword --> EmailSystem
UC_RepairRequest --> EmailSystem
UC_ConfirmOrder --> EmailSystem
UC_ReceiveNotifications --> EmailSystem
UC_RunCrawler --> WebCrawler
UC_ViewCrawledData --> WebCrawler

' === RELATIONS INCLUDE/EXTEND ===
UC_Checkout ..> UC_Payment : <<include>>
UC_Checkout ..> UC_ConfirmOrder : <<include>>
UC_Register ..> EmailSystem : <<include>>
UC_RepairRequest ..> EmailSystem : <<include>>
UC_ConfirmOrder ..> EmailSystem : <<include>>
UC_ManageSpecs ..> UC_ViewSpecs : <<extend>>
UC_AddProduct ..> UC_ManageSpecs : <<include>>
UC_EditProduct ..> UC_ManageSpecs : <<include>>
UC_Login ..> UC_ManageProfile : <<extend>>
UC_ViewProductDetails ..> UC_ViewSpecs : <<include>>
UC_ViewProductDetails ..> UC_AddToCart : <<extend>>
UC_ManageCart ..> UC_Checkout : <<extend>>
UC_Dashboard ..> UC_ViewAnalytics : <<include>>
UC_ManageUsers ..> UC_ManageRoles : <<extend>>

' === NOTES EXPLICATIVES ===
note right of UC_ManageSpecs
  Gestion des spécifications
  par catégorie:
  - Laptops: CPU, RAM, SSD, etc.
  - Desktops: CPU, GPU, PSU, etc.
  - Components: Type, Model, etc.
  - Accessories: Type, Connectivity, etc.
end note

note right of UC_Payment
  Intégration avec
  système de paiement
  externe (Stripe)
end note

note right of UC_RunCrawler
  Service automatisé
  d'extraction de données
  avec Puppeteer
end note

note bottom of Admin
  Accès complet à toutes
  les fonctionnalités
  d'administration
end note

note bottom of Manager
  Gestion des produits
  et commandes
  uniquement
end note

note bottom of Customer
  Accès aux fonctionnalités
  e-commerce après
  authentification
end note

note bottom of Guest
  Accès limité aux
  fonctionnalités publiques
  uniquement
end note

@enduml
