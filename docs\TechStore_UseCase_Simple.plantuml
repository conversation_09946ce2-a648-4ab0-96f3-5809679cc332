@startuml TechStore_Simple_UseCase

!theme plain
skinparam backgroundColor #FFFFFF
skinparam actor {
    BackgroundColor #E3F2FD
    BorderColor #1976D2
    FontSize 12
}
skinparam usecase {
    BackgroundColor #F3E5F5
    BorderColor #7B1FA2
    FontSize 10
}

title TechStore - Diagramme de Cas d'Usage Simplifié

' Acteurs
actor "👥 Visiteur" as Guest
actor "👤 Client" as Customer  
actor "👨‍💼 Admin" as Admin

' Systèmes externes
actor "📧 Email" as Email
actor "💳 Paiement" as Payment

' Cas d'usage principaux
rectangle "TechStore System" {
    
    ' Authentification
    usecase "Se connecter" as Login
    usecase "S'inscrire" as Register
    usecase "Se déconnecter" as Logout
    
    ' Produits
    usecase "Consulter produits" as ViewProducts
    usecase "Rechercher produits" as SearchProducts
    usecase "Voir détails produit" as ProductDetails
    usecase "Gérer produits" as ManageProducts
    usecase "Gérer spécifications" as ManageSpecs
    
    ' E-commerce
    usecase "Ajouter au panier" as AddToCart
    usecase "Passer commande" as Checkout
    usecase "Effectuer paiement" as PaymentUC
    usecase "Historique commandes" as OrderHistory
    
    ' Administration
    usecase "Gérer utilisateurs" as ManageUsers
    usecase "Gérer commandes" as ManageOrders
    usecase "Tableau de bord" as Dashboard
    usecase "Web Crawler" as WebCrawler
    
    ' Services
    usecase "Demande réparation" as RepairRequest
    usecase "Contact" as Contact
    
    ' Fonctionnalités avancées
    usecase "Liste souhaits" as Wishlist
    usecase "Comparer produits" as Compare
}

' Relations Visiteur
Guest --> ViewProducts
Guest --> SearchProducts
Guest --> ProductDetails
Guest --> Register
Guest --> Login
Guest --> Contact

' Relations Client
Customer --> ViewProducts
Customer --> SearchProducts
Customer --> ProductDetails
Customer --> AddToCart
Customer --> Checkout
Customer --> PaymentUC
Customer --> OrderHistory
Customer --> Logout
Customer --> RepairRequest
Customer --> Wishlist
Customer --> Compare

' Relations Admin
Admin --> ManageProducts
Admin --> ManageSpecs
Admin --> ManageUsers
Admin --> ManageOrders
Admin --> Dashboard
Admin --> WebCrawler

' Relations systèmes externes
PaymentUC --> Payment
RepairRequest --> Email
Register --> Email

' Relations include/extend
Checkout ..> PaymentUC : <<include>>
RepairRequest ..> Email : <<include>>
ManageProducts ..> ManageSpecs : <<include>>

' Héritage d'acteurs
Customer --|> Guest : hérite

' Notes
note right of ManageSpecs
  Spécifications par catégorie:
  • Laptops: CPU, RAM, SSD, Display
  • Desktops: CPU, GPU, RAM, Storage
  • Components: Type, Model, Power
  • Accessories: Type, Connectivity
end note

note right of WebCrawler
  Service automatisé
  d'extraction de données
  produits avec Puppeteer
end note

note bottom of Admin
  Accès complet:
  • Gestion produits & spécifications
  • Administration utilisateurs
  • Analytics & rapports
  • Configuration système
end note

@enduml
