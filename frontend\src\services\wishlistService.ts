import api from './api';
import { Product } from './productService';

export interface WishlistItem {
  _id: string;
  user: string;
  product: Product;
  createdAt: string;
  updatedAt: string;
}

export interface WishlistResponse {
  success: boolean;
  wishlist: WishlistItem[];
  count: number;
}

export interface WishlistAddResponse {
  success: boolean;
  message: string;
  data: WishlistItem;
}

export interface WishlistCheckResponse {
  success: boolean;
  inWishlist: boolean;
}

class WishlistService {
  // Get user's wishlist
  async getWishlist(): Promise<WishlistItem[]> {
    try {
      const response = await api.get<WishlistResponse>('/wishlist');
      return response.data.wishlist;
    } catch (error: any) {
      console.error('Error fetching wishlist:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch wishlist');
    }
  }

  // Add product to wishlist
  async addToWishlist(productId: string): Promise<WishlistItem> {
    try {
      const response = await api.post<WishlistAddResponse>('/wishlist', { productId });
      return response.data.data;
    } catch (error: any) {
      console.error('Error adding to wishlist:', error);
      throw new Error(error.response?.data?.message || 'Failed to add to wishlist');
    }
  }

  // Remove product from wishlist
  async removeFromWishlist(productId: string): Promise<void> {
    try {
      await api.delete(`/wishlist/${productId}`);
    } catch (error: any) {
      console.error('Error removing from wishlist:', error);
      throw new Error(error.response?.data?.message || 'Failed to remove from wishlist');
    }
  }

  // Clear entire wishlist
  async clearWishlist(): Promise<void> {
    try {
      await api.delete('/wishlist');
    } catch (error: any) {
      console.error('Error clearing wishlist:', error);
      throw new Error(error.response?.data?.message || 'Failed to clear wishlist');
    }
  }

  // Check if product is in wishlist
  async isInWishlist(productId: string): Promise<boolean> {
    try {
      const response = await api.get<WishlistCheckResponse>(`/wishlist/check/${productId}`);
      return response.data.inWishlist;
    } catch (error: any) {
      console.error('Error checking wishlist:', error);
      return false;
    }
  }
}

export const wishlistService = new WishlistService();
export default wishlistService;
