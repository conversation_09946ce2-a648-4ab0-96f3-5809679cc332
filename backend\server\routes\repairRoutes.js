import express from 'express';
import emailService from '../services/emailService.js';
import RepairRequest from '../models/repairRequestModel.js';
import { protect, admin } from '../middleware/auth.js';

const router = express.Router();

// @desc    Submit a repair request
// @route   POST /api/repair/request
// @access  Public
router.post('/request', async (req, res) => {
  try {
    const {
      name,
      email,
      phone,
      serviceType,
      repairType,
      description,
      laptopBrand,
      laptopModel,
      laptopAge,
      desktopType,
      operatingSystem,
      tabletBrand,
      tabletModel,
      tabletIssue,
      phoneBrand,
      phoneModel,
      phoneCarrier
    } = req.body;

    // Validate required fields
    if (!name || !email || !phone || !serviceType || !repairType || !description) {
      return res.status(400).json({
        message: 'Missing required fields: name, email, phone, serviceType, repairType, description'
      });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return res.status(400).json({
        message: 'Invalid email format'
      });
    }

    // Validate phone number (basic validation)
    if (phone.length < 10) {
      return res.status(400).json({
        message: 'Phone number must be at least 10 characters'
      });
    }

    // Create repair request in database
    const repairRequest = new RepairRequest({
      name,
      email,
      phone,
      serviceType,
      repairType,
      description,
      laptopBrand,
      laptopModel,
      laptopAge,
      desktopType,
      operatingSystem,
      tabletBrand,
      tabletModel,
      tabletIssue,
      phoneBrand,
      phoneModel,
      phoneCarrier,
      status: 'PENDING', // Default status
      priority: 'NORMAL' // Default priority
    });

    // Calculate estimated cost
    repairRequest.calculateEstimate();

    // Save to database
    const savedRequest = await repairRequest.save();

    console.log('Processing repair request:', {
      requestNumber: savedRequest.requestNumber,
      name,
      email,
      serviceType,
      repairType,
      status: savedRequest.status
    });

    // Prepare email data
    const repairData = {
      name,
      email,
      phone,
      serviceType,
      repairType,
      description,
      laptopBrand,
      laptopModel,
      laptopAge,
      desktopType,
      operatingSystem,
      tabletBrand,
      tabletModel,
      tabletIssue,
      phoneBrand,
      phoneModel,
      phoneCarrier,
      requestNumber: savedRequest.requestNumber,
      estimatedCost: savedRequest.estimatedCost,
      submittedAt: savedRequest.createdAt.toISOString()
    };

    // Send <NAME_EMAIL>
    const emailResult = await emailService.sendRepairRequest(repairData);

    // Update email tracking in database
    savedRequest.emailSent = emailResult.success;
    if (emailResult.messageId) {
      savedRequest.emailMessageId = emailResult.messageId;
    }
    await savedRequest.save();

    if (emailResult.success) {
      // Send confirmation email to customer (optional, won't fail if it doesn't work)
      emailService.sendConfirmationEmail(email, repairData).catch(error => {
        console.log('Confirmation email failed (non-critical):', error.message);
      });

      res.status(201).json({
        success: true,
        message: 'Repair request submitted successfully',
        data: {
          requestId: savedRequest._id,
          requestNumber: savedRequest.requestNumber,
          status: savedRequest.status,
          estimatedCost: savedRequest.estimatedCost,
          submittedAt: savedRequest.createdAt,
          customerEmail: email,
          serviceType,
          repairType,
          emailSent: savedRequest.emailSent
        }
      });
    } else {
      // Even if email fails, we keep the database record
      res.status(201).json({
        success: true,
        message: 'Repair request saved successfully (email delivery failed)',
        data: {
          requestId: savedRequest._id,
          requestNumber: savedRequest.requestNumber,
          status: savedRequest.status,
          estimatedCost: savedRequest.estimatedCost,
          submittedAt: savedRequest.createdAt,
          customerEmail: email,
          serviceType,
          repairType,
          emailSent: false,
          warning: 'Email notification could not be sent'
        }
      });
    }

  } catch (error) {
    console.error('Error processing repair request:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to submit repair request',
      error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
    });
  }
});

// @desc    Get repair service types (for frontend)
// @route   GET /api/repair/services
// @access  Public
router.get('/services', (req, res) => {
  try {
    const repairServices = [
      {
        id: 'laptop',
        name: 'Laptop Repair',
        description: 'Complete laptop repair services',
        icon: 'laptop'
      },
      {
        id: 'desktop',
        name: 'Desktop Repair',
        description: 'Desktop computer repair and maintenance',
        icon: 'desktop'
      },
      {
        id: 'tablet',
        name: 'Tablet Repair',
        description: 'Tablet screen and hardware repair',
        icon: 'tablet'
      },
      {
        id: 'phone',
        name: 'Phone Repair',
        description: 'Smartphone repair services',
        icon: 'phone'
      }
    ];

    const repairTypes = {
      laptop: [
        'Screen Replacement',
        'Keyboard Repair',
        'Battery Replacement',
        'Hard Drive Replacement',
        'RAM Upgrade',
        'Virus Removal',
        'Operating System Installation',
        'Hardware Diagnosis',
        'Overheating Issues',
        'Power Jack Repair'
      ],
      desktop: [
        'Hardware Upgrade',
        'Virus Removal',
        'Operating System Installation',
        'Hard Drive Replacement',
        'RAM Installation',
        'Graphics Card Installation',
        'Power Supply Replacement',
        'Motherboard Repair',
        'CPU Replacement',
        'System Optimization'
      ],
      tablet: [
        'Screen Replacement',
        'Battery Replacement',
        'Charging Port Repair',
        'Software Issues',
        'Water Damage Repair',
        'Speaker Repair',
        'Camera Repair',
        'Button Repair',
        'Wi-Fi Issues',
        'Factory Reset'
      ],
      phone: [
        'Screen Replacement',
        'Battery Replacement',
        'Charging Port Repair',
        'Camera Repair',
        'Speaker Repair',
        'Water Damage Repair',
        'Software Issues',
        'Button Repair',
        'Microphone Repair',
        'Factory Reset'
      ]
    };

    res.status(200).json({
      success: true,
      data: {
        services: repairServices,
        repairTypes: repairTypes
      }
    });

  } catch (error) {
    console.error('Error fetching repair services:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch repair services'
    });
  }
});

// @desc    Test email functionality
// @route   POST /api/repair/test-email
// @access  Public (for development only)
router.post('/test-email', async (req, res) => {
  try {
    if (process.env.NODE_ENV === 'production') {
      return res.status(403).json({
        message: 'Test endpoint not available in production'
      });
    }

    const testData = {
      name: 'Test User',
      email: '<EMAIL>',
      phone: '1234567890',
      serviceType: 'laptop',
      repairType: 'Screen Replacement',
      description: 'This is a test repair request to verify email functionality.',
      laptopBrand: 'Dell',
      laptopModel: 'XPS 13',
      laptopAge: '2 years'
    };

    const result = await emailService.sendRepairRequest(testData);

    res.status(200).json({
      success: true,
      message: 'Test email sent successfully',
      data: result
    });

  } catch (error) {
    console.error('Error sending test email:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to send test email',
      error: error.message
    });
  }
});

// @desc    Get all repair requests (Admin only)
// @route   GET /api/repair/requests
// @access  Private/Admin
router.get('/requests', protect, admin, async (req, res) => {
  try {
    const { status, priority, page = 1, limit = 10 } = req.query;

    // Build query
    const query = {};
    if (status && status !== 'all') {
      query.status = status;
    }
    if (priority && priority !== 'all') {
      query.priority = priority;
    }

    // Calculate pagination
    const skip = (page - 1) * limit;

    // Get repair requests with pagination
    const requests = await RepairRequest.find(query)
      .populate('assignedTechnician', 'name email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(parseInt(limit));

    // Get total count for pagination
    const total = await RepairRequest.countDocuments(query);

    res.json({
      success: true,
      data: {
        requests,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total,
          pages: Math.ceil(total / limit)
        }
      }
    });
  } catch (error) {
    console.error('Error fetching repair requests:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch repair requests',
      error: error.message
    });
  }
});

// @desc    Get single repair request (Admin only)
// @route   GET /api/repair/requests/:id
// @access  Private/Admin
router.get('/requests/:id', protect, admin, async (req, res) => {
  try {
    const request = await RepairRequest.findById(req.params.id)
      .populate('assignedTechnician', 'name email');

    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Repair request not found'
      });
    }

    res.json({
      success: true,
      data: request
    });
  } catch (error) {
    console.error('Error fetching repair request:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch repair request',
      error: error.message
    });
  }
});

// @desc    Update repair request status (Admin only)
// @route   PUT /api/repair/requests/:id/status
// @access  Private/Admin
router.put('/requests/:id/status', protect, admin, async (req, res) => {
  try {
    const { status, notes } = req.body;

    if (!status) {
      return res.status(400).json({
        success: false,
        message: 'Status is required'
      });
    }

    const validStatuses = ['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid status. Must be one of: ' + validStatuses.join(', ')
      });
    }

    const request = await RepairRequest.findById(req.params.id);
    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Repair request not found'
      });
    }

    await request.updateStatus(status, notes);

    res.json({
      success: true,
      message: 'Repair request status updated successfully',
      data: {
        requestId: request._id,
        requestNumber: request.requestNumber,
        status: request.status,
        updatedAt: request.updatedAt
      }
    });
  } catch (error) {
    console.error('Error updating repair request status:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update repair request status',
      error: error.message
    });
  }
});

// @desc    Assign technician to repair request (Admin only)
// @route   PUT /api/repair/requests/:id/assign
// @access  Private/Admin
router.put('/requests/:id/assign', protect, admin, async (req, res) => {
  try {
    const { technicianId, notes } = req.body;

    if (!technicianId) {
      return res.status(400).json({
        success: false,
        message: 'Technician ID is required'
      });
    }

    const request = await RepairRequest.findById(req.params.id);
    if (!request) {
      return res.status(404).json({
        success: false,
        message: 'Repair request not found'
      });
    }

    await request.assignTechnician(technicianId, notes);

    const updatedRequest = await RepairRequest.findById(req.params.id)
      .populate('assignedTechnician', 'name email');

    res.json({
      success: true,
      message: 'Technician assigned successfully',
      data: updatedRequest
    });
  } catch (error) {
    console.error('Error assigning technician:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to assign technician',
      error: error.message
    });
  }
});

// @desc    Get repair request statistics (Admin only)
// @route   GET /api/repair/stats
// @access  Private/Admin
router.get('/stats', protect, admin, async (req, res) => {
  try {
    const stats = await RepairRequest.getStats();

    // Get total counts
    const totalRequests = await RepairRequest.countDocuments();
    const pendingRequests = await RepairRequest.countDocuments({ status: 'PENDING' });
    const inProgressRequests = await RepairRequest.countDocuments({ status: 'IN_PROGRESS' });
    const completedRequests = await RepairRequest.countDocuments({ status: 'COMPLETED' });
    const cancelledRequests = await RepairRequest.countDocuments({ status: 'CANCELLED' });

    res.json({
      success: true,
      data: {
        totalRequests,
        statusBreakdown: {
          pending: pendingRequests,
          inProgress: inProgressRequests,
          completed: completedRequests,
          cancelled: cancelledRequests
        },
        detailedStats: stats
      }
    });
  } catch (error) {
    console.error('Error fetching repair statistics:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch repair statistics',
      error: error.message
    });
  }
});

export default router;
