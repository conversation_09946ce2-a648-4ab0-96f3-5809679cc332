import mongoose from 'mongoose';

const repairRequestSchema = mongoose.Schema(
  {
    // Customer Information
    name: {
      type: String,
      required: true,
    },
    email: {
      type: String,
      required: true,
    },
    phone: {
      type: String,
      required: true,
    },
    
    // Service Information
    serviceType: {
      type: String,
      required: true,
      enum: ['laptop', 'desktop', 'tablet', 'phone'],
    },
    repairType: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    
    // Status Management
    status: {
      type: String,
      required: true,
      default: 'PENDING',
      enum: ['PENDING', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED'],
    },
    priority: {
      type: String,
      default: 'NORMAL',
      enum: ['LOW', 'NORMAL', 'HIGH', 'URGENT'],
    },
    
    // Cost Information
    estimatedCost: {
      type: Number,
      default: 0,
    },
    actualCost: {
      type: Number,
      default: 0,
    },
    
    // Device-specific Information (Laptop)
    laptopBrand: {
      type: String,
    },
    laptopModel: {
      type: String,
    },
    laptopAge: {
      type: String,
    },
    
    // Device-specific Information (Desktop)
    desktopType: {
      type: String,
    },
    operatingSystem: {
      type: String,
    },
    
    // Device-specific Information (Tablet)
    tabletBrand: {
      type: String,
    },
    tabletModel: {
      type: String,
    },
    tabletIssue: {
      type: String,
    },
    
    // Device-specific Information (Phone)
    phoneBrand: {
      type: String,
    },
    phoneModel: {
      type: String,
    },
    phoneCarrier: {
      type: String,
    },
    
    // Technical Information
    technicianNotes: {
      type: String,
      default: '',
    },
    customerNotes: {
      type: String,
      default: '',
    },
    
    // Assignment
    assignedTechnician: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
    },
    
    // Tracking
    requestNumber: {
      type: String,
      unique: true,
    },
    
    // Email tracking
    emailSent: {
      type: Boolean,
      default: false,
    },
    emailMessageId: {
      type: String,
    },
  },
  {
    timestamps: true,
  }
);

// Generate request number before saving
repairRequestSchema.pre('save', async function(next) {
  if (!this.requestNumber) {
    const year = new Date().getFullYear();
    const count = await this.constructor.countDocuments({
      createdAt: {
        $gte: new Date(year, 0, 1),
        $lt: new Date(year + 1, 0, 1)
      }
    });
    this.requestNumber = `REP-${year}-${String(count + 1).padStart(4, '0')}`;
  }
  next();
});

// Instance method to update status
repairRequestSchema.methods.updateStatus = function(newStatus, notes = '') {
  this.status = newStatus;
  if (notes) {
    this.technicianNotes = this.technicianNotes ? 
      `${this.technicianNotes}\n\n[${new Date().toISOString()}] ${notes}` : 
      `[${new Date().toISOString()}] ${notes}`;
  }
  return this.save();
};

// Instance method to assign technician
repairRequestSchema.methods.assignTechnician = function(technicianId, notes = '') {
  this.assignedTechnician = technicianId;
  if (notes) {
    this.technicianNotes = this.technicianNotes ? 
      `${this.technicianNotes}\n\n[${new Date().toISOString()}] Assigned to technician: ${notes}` : 
      `[${new Date().toISOString()}] Assigned to technician: ${notes}`;
  }
  return this.save();
};

// Instance method to calculate estimate
repairRequestSchema.methods.calculateEstimate = function() {
  // Basic estimation logic based on service and repair type
  const baseCosts = {
    laptop: {
      'Screen Replacement': 150,
      'Keyboard Repair': 80,
      'Battery Replacement': 100,
      'Hard Drive Replacement': 120,
      'RAM Upgrade': 90,
      'Virus Removal': 60,
      'Operating System Installation': 70,
      'Hardware Diagnosis': 50,
      'Overheating Issues': 90,
      'Power Jack Repair': 110
    },
    desktop: {
      'Hardware Upgrade': 100,
      'Virus Removal': 60,
      'Operating System Installation': 70,
      'Hard Drive Replacement': 100,
      'RAM Installation': 80,
      'Graphics Card Installation': 120,
      'Power Supply Replacement': 90,
      'Motherboard Repair': 200,
      'CPU Replacement': 150,
      'System Optimization': 50
    },
    tablet: {
      'Screen Replacement': 120,
      'Battery Replacement': 80,
      'Charging Port Repair': 70,
      'Software Issues': 50,
      'Water Damage Repair': 150,
      'Speaker Repair': 60,
      'Camera Repair': 90,
      'Button Repair': 50,
      'Wi-Fi Issues': 60,
      'Factory Reset': 30
    },
    phone: {
      'Screen Replacement': 100,
      'Battery Replacement': 70,
      'Charging Port Repair': 60,
      'Camera Repair': 80,
      'Speaker Repair': 50,
      'Water Damage Repair': 120,
      'Software Issues': 40,
      'Button Repair': 40,
      'Microphone Repair': 50,
      'Factory Reset': 25
    }
  };

  const estimate = baseCosts[this.serviceType]?.[this.repairType] || 50;
  this.estimatedCost = estimate;
  return estimate;
};

// Static method to get requests by status
repairRequestSchema.statics.getByStatus = function(status) {
  return this.find({ status })
    .populate('assignedTechnician', 'name email')
    .sort({ createdAt: -1 });
};

// Static method to get requests by priority
repairRequestSchema.statics.getByPriority = function(priority) {
  return this.find({ priority })
    .populate('assignedTechnician', 'name email')
    .sort({ createdAt: -1 });
};

// Static method to get repair statistics
repairRequestSchema.statics.getStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$status',
        count: { $sum: 1 },
        totalEstimatedCost: { $sum: '$estimatedCost' },
        totalActualCost: { $sum: '$actualCost' }
      }
    }
  ]);
};

const RepairRequest = mongoose.model('RepairRequest', repairRequestSchema);

export default RepairRequest;
