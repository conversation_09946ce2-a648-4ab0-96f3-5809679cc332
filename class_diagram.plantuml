@startuml TechStore Class Diagram
!define ENTITY class
!define ENUM enum

title TechStore - Complete Class Diagram

' === USER MANAGEMENT ===
ENTITY User {
  - _id: ObjectId
  - name: String
  - email: String
  - password: String
  - role: UserRole
  - status: UserStatus
  - lastLogin: Date
  - createdAt: Date
  - updatedAt: Date
  --
  + validatePassword(password: String): Boolean
  + generateToken(): String
  + updateLastLogin(): void
  + isAdmin(): Boolean
  + isManager(): Boolean
  + isCustomer(): Boolean
}

ENUM UserRole {
  ADMIN
  MANAGER
  CUSTOMER
}

ENUM UserStatus {
  ACTIVE
  INACTIVE
}

' === PRODUCT MANAGEMENT ===
ENTITY Product {
  - _id: ObjectId
  - name: String
  - description: String
  - price: Number
  - salePrice: Number
  - category: String
  - image: String
  - stock: Number
  - isNewProduct: Boolean
  - isOnSale: Boolean
  - isFeatured: Boolean
  - rating: Number
  - reviews: Number
  - specifications: Map<String, String>
  - createdAt: Date
  - updatedAt: Date
  --
  + getSpecificationTemplate(): Object
  + validateSpecifications(): ValidationResult
  + updateStock(quantity: Number): void
  + calculateDiscountedPrice(): Number
  + isInStock(): Boolean
  + getEffectivePrice(): Number
}

' === WISHLIST MANAGEMENT ===
ENTITY Wishlist {
  - _id: ObjectId
  - user: ObjectId
  - product: ObjectId
  - createdAt: Date
  - updatedAt: Date
  --
  + {static} getUserWishlist(userId: ObjectId): Wishlist[]
  + {static} addToWishlist(userId: ObjectId, productId: ObjectId): Wishlist
  + {static} removeFromWishlist(userId: ObjectId, productId: ObjectId): void
  + {static} isInUserWishlist(userId: ObjectId, productId: ObjectId): Boolean
  + {static} getUserWishlistCount(userId: ObjectId): Number
  + {static} clearUserWishlist(userId: ObjectId): void
}

' === ORDER MANAGEMENT ===
ENTITY Order {
  - _id: ObjectId
  - orderNumber: String
  - user: ObjectId
  - items: OrderItem[]
  - status: OrderStatus
  - subtotal: Number
  - tax: Number
  - shipping: Number
  - total: Number
  - shippingAddress: ShippingAddress
  - paymentMethod: String
  - paymentStatus: PaymentStatus
  - trackingNumber: String
  - notes: String
  - createdAt: Date
  - updatedAt: Date
  --
  + updateStatus(status: OrderStatus, trackingNumber: String): Promise<Order>
  + {static} getByUser(userId: ObjectId, status: OrderStatus): Promise<Order[]>
  + {static} getStats(): Promise<Object[]>
  + calculateTotal(): Number
  + generateOrderNumber(): String
  + age(): Number
}

ENTITY OrderItem {
  - product: ObjectId
  - name: String
  - price: Number
  - quantity: Number
  - image: String
  --
  + calculateSubtotal(): Number
}

ENTITY ShippingAddress {
  - name: String
  - address: String
  - city: String
  - postalCode: String
  - country: String
  --
  + validateAddress(): Boolean
  + formatAddress(): String
}

ENUM OrderStatus {
  PENDING
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
}

ENUM PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}

' === REPAIR MANAGEMENT ===
ENTITY RepairRequest {
  - _id: ObjectId
  - requestNumber: String
  - name: String
  - email: String
  - phone: String
  - serviceType: ServiceType
  - repairType: String
  - description: String
  - status: RepairStatus
  - priority: RepairPriority
  - estimatedCost: Number
  - actualCost: Number
  - laptopBrand: String
  - laptopModel: String
  - laptopAge: String
  - desktopType: String
  - operatingSystem: String
  - tabletBrand: String
  - tabletModel: String
  - tabletIssue: String
  - phoneBrand: String
  - phoneModel: String
  - phoneCarrier: String
  - technicianNotes: String
  - customerNotes: String
  - assignedTechnician: ObjectId
  - emailSent: Boolean
  - emailMessageId: String
  - createdAt: Date
  - updatedAt: Date
  --
  + updateStatus(status: RepairStatus, notes: String): Promise<RepairRequest>
  + calculateEstimate(): Number
  + assignTechnician(technicianId: ObjectId, notes: String): Promise<RepairRequest>
  + generateRequestNumber(): String
  + {static} getStats(): Promise<Object[]>
  + {static} getByStatus(status: RepairStatus): Promise<RepairRequest[]>
  + {static} getByPriority(priority: RepairPriority): Promise<RepairRequest[]>
}

ENUM ServiceType {
  LAPTOP
  DESKTOP
  TABLET
  PHONE
}

ENUM RepairStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

ENUM RepairPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}



' === SPECIFICATION TEMPLATES ===
ENTITY SpecificationTemplate {
  - category: String
  - required: String[]
  - optional: String[]
  - schema: Map<String, Object>
  --
  + {static} getTemplate(category: String): SpecificationTemplate
  + validateSpecifications(specs: Object): ValidationResult
  + getRequiredFields(): String[]
  + getOptionalFields(): String[]
}

ENTITY ValidationResult {
  - isValid: Boolean
  - errors: String[]
  - warnings: String[]
  --
  + hasErrors(): Boolean
  + hasWarnings(): Boolean
  + getErrorMessages(): String[]
}

' === CONTACT MANAGEMENT ===
ENTITY ContactMessage {
  - _id: ObjectId
  - name: String
  - email: String
  - subject: String
  - message: String
  - status: String
  - response: String
  - respondedBy: ObjectId
  - respondedAt: Date
  - createdAt: Date
  - updatedAt: Date
  --
  + markAsRead(): void
  + addResponse(response: String, adminId: ObjectId): void
}

' === RELATIONSHIPS ===
User ||--o{ Wishlist : "has many"
Product ||--o{ Wishlist : "appears in many"

User ||--o{ Order : "places many"
Order ||--o{ OrderItem : "contains many"
Product ||--o{ OrderItem : "referenced in many"
Order ||--|| ShippingAddress : "has one"

User ||--o{ RepairRequest : "submits many"

User ||--o{ ContactMessage : "sends many"
User ||--o{ ContactMessage : "responds to many"

User }|--|| UserRole : "has one"
User }|--|| UserStatus : "has one"
Order }|--|| OrderStatus : "has one"
Order }|--|| PaymentStatus : "has one"
RepairRequest }|--|| RepairStatus : "has one"
RepairRequest }|--|| RepairPriority : "has one"
RepairRequest }|--|| ServiceType : "has one"

' Additional relationships
RepairRequest }|--|| User : "assigned technician"
OrderItem }|--|| Product : "references"
Wishlist }|--|| User : "belongs to"
Wishlist }|--|| Product : "contains"
Order }|--|| User : "belongs to"
ContactMessage }|--|| User : "responded by"
Product ||--|| ValidationResult : "validates with"
Product }|--|| SpecificationTemplate : "uses template"

' === NOTES ===
note right of User : "Central entity for authentication,\nuser management, and role-based\naccess control with Admin,\nManager, and Customer roles"

note right of Product : "Complete product catalog\nwith dynamic specifications,\ninventory management,\nand category-based validation"

note right of Wishlist : "Personal user wishlists\nwith authentication required,\npersistent storage,\nand duplicate prevention"

note right of Order : "Complete e-commerce order\nlifecycle with payment tracking,\nstatus updates, auto-generated\norder numbers, and shipping"

note right of RepairRequest : "Comprehensive repair service\nwith status management,\ntechnician assignment,\ncost estimation, and\ndevice-specific fields"

note right of ContactMessage : "Customer support system\nwith admin response\ncapabilities and\nstatus tracking"

note right of SpecificationTemplate : "Dynamic product specification\nvalidation system with\ncategory-based templates\nand flexible validation rules"

@enduml
