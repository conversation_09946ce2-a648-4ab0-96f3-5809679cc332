@startuml TechStore Class Diagram
!define ENTITY class
!define ENUM enum

title TechStore - Class Diagram

' === USER MANAGEMENT ===
ENTITY User {
  - _id: ObjectId
  - name: String
  - email: String
  - password: String
  - role: String
  - status: String
  - lastLogin: Date
  - createdAt: Date
  - updatedAt: Date
  --
  + validatePassword(password: String): Boolean
  + generateToken(): String
  + updateLastLogin(): void
}

ENUM UserRole {
  ADMIN
  MANAGER
  CUSTOMER
}

ENUM UserStatus {
  ACTIVE
  INACTIVE
}

' === PRODUCT MANAGEMENT ===
ENTITY Product {
  - _id: ObjectId
  - name: String
  - description: String
  - price: Number
  - salePrice: Number
  - category: String
  - image: String
  - stock: Number
  - isNewProduct: Boolean
  - isOnSale: Boolean
  - isFeatured: Boolean
  - rating: Number
  - reviews: Number
  - specifications: Map<String, String>
  - createdAt: Date
  - updatedAt: Date
  --
  + getSpecificationTemplate(): Object
  + validateSpecifications(): Boolean
  + updateStock(quantity: Number): void
  + calculateDiscountedPrice(): Number
}

' === WISHLIST MANAGEMENT ===
ENTITY Wishlist {
  - _id: ObjectId
  - user: ObjectId
  - product: ObjectId
  - createdAt: Date
  - updatedAt: Date
  --
  + {static} getUserWishlist(userId: ObjectId): Wishlist[]
  + {static} isInUserWishlist(userId: ObjectId, productId: ObjectId): Boolean
  + {static} getUserWishlistCount(userId: ObjectId): Number
  + {static} clearUserWishlist(userId: ObjectId): void
}

' === ORDER MANAGEMENT ===
ENTITY Order {
  - _id: ObjectId
  - orderNumber: String
  - user: ObjectId
  - items: OrderItem[]
  - status: String
  - subtotal: Number
  - tax: Number
  - shipping: Number
  - total: Number
  - shippingAddress: ShippingAddress
  - paymentMethod: String
  - paymentStatus: String
  - trackingNumber: String
  - notes: String
  - createdAt: Date
  - updatedAt: Date
  --
  + updateStatus(status: String, trackingNumber: String): void
  + {static} getByUser(userId: ObjectId, status: String): Order[]
  + {static} getStats(): Object[]
  + calculateTotal(): Number
  + generateOrderNumber(): String
}

ENTITY OrderItem {
  - product: ObjectId
  - name: String
  - price: Number
  - quantity: Number
  - image: String
  --
  + calculateSubtotal(): Number
}

ENTITY ShippingAddress {
  - name: String
  - address: String
  - city: String
  - postalCode: String
  - country: String
  --
  + validateAddress(): Boolean
  + formatAddress(): String
}

ENUM OrderStatus {
  PENDING
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
}

ENUM PaymentStatus {
  PENDING
  PAID
  FAILED
  REFUNDED
}

' === REPAIR MANAGEMENT ===
ENTITY RepairRequest {
  - _id: ObjectId
  - user: ObjectId
  - deviceType: String
  - brand: String
  - model: String
  - issueDescription: String
  - status: String
  - priority: String
  - estimatedCost: Number
  - actualCost: Number
  - technicianNotes: String
  - customerNotes: String
  - createdAt: Date
  - updatedAt: Date
  --
  + updateStatus(status: String): void
  + calculateEstimate(): Number
  + assignTechnician(technicianId: ObjectId): void
}

ENUM RepairStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  CANCELLED
}

' === CONTACT MANAGEMENT ===
ENTITY ContactMessage {
  - _id: ObjectId
  - name: String
  - email: String
  - subject: String
  - message: String
  - status: String
  - response: String
  - respondedBy: ObjectId
  - respondedAt: Date
  - createdAt: Date
  - updatedAt: Date
  --
  + markAsRead(): void
  + addResponse(response: String, adminId: ObjectId): void
}

' === RELATIONSHIPS ===
User ||--o{ Wishlist : "has many"
Product ||--o{ Wishlist : "appears in many"

User ||--o{ Order : "places many"
Order ||--o{ OrderItem : "contains many"
Product ||--o{ OrderItem : "referenced in many"
Order ||--|| ShippingAddress : "has one"

User ||--o{ RepairRequest : "submits many"

User ||--o{ ContactMessage : "sends many"
User ||--o{ ContactMessage : "responds to many"

User }|--|| UserRole : "has one"
User }|--|| UserStatus : "has one"
Order }|--|| OrderStatus : "has one"
Order }|--|| PaymentStatus : "has one"
RepairRequest }|--|| RepairStatus : "has one"

' === NOTES ===
note right of User : "Handles authentication,\nprofile management,\nand role-based access"

note right of Product : "Core product catalog\nwith specifications\nand inventory management"

note right of Wishlist : "Personal user wishlists\nwith authentication\nrequired"

note right of Order : "Complete order lifecycle\nwith payment tracking\nand status updates"

note right of RepairRequest : "Device repair service\nrequests and tracking"

@enduml
