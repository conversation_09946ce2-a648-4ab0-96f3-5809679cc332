
import express from 'express';
import Product from '../models/productModel.js';
import { getSpecificationTemplate, validateSpecifications, getSpecificationKeys } from '../schemas/productSpecifications.js';
import { protect, admin } from '../middleware/auth.js';

const router = express.Router();

// @desc    Get all products
// @route   GET /api/products
// @access  Public
router.get('/', async (req, res) => {
  try {
    const products = await Product.find({});
    res.json(products);
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// @desc    Get single product
// @route   GET /api/products/:id
// @access  Public
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    // Check if ID is valid
    if (!id || id === 'undefined' || id === 'null') {
      return res.status(400).json({ message: 'Invalid product ID' });
    }

    // Check if ID is a valid MongoDB ObjectId
    if (!id.match(/^[0-9a-fA-F]{24}$/)) {
      return res.status(400).json({ message: 'Invalid product ID format' });
    }

    const product = await Product.findById(id);

    if (product) {
      res.json(product);
    } else {
      res.status(404).json({ message: 'Product not found' });
    }
  } catch (error) {
    console.error('Error fetching product by ID:', error);
    res.status(500).json({ message: 'Server error while fetching product' });
  }
});

// @desc    Create a product
// @route   POST /api/products
// @access  Private/Admin
router.post('/', protect, admin, async (req, res) => {
  try {
    const productData = { ...req.body };

    // Convert specifications object to Map if provided
    if (productData.specifications && typeof productData.specifications === 'object') {
      productData.specifications = new Map(Object.entries(productData.specifications));
    }

    const product = new Product(productData);
    const createdProduct = await product.save();
    res.status(201).json(createdProduct);
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// @desc    Update a product
// @route   PUT /api/products/:id
// @access  Private/Admin
router.put('/:id', protect, admin, async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);

    if (product) {
      const updateData = { ...req.body };

      // Convert specifications object to Map if provided
      if (updateData.specifications && typeof updateData.specifications === 'object') {
        updateData.specifications = new Map(Object.entries(updateData.specifications));
      }

      Object.assign(product, updateData);
      const updatedProduct = await product.save();
      res.json(updatedProduct);
    } else {
      res.status(404).json({ message: 'Product not found' });
    }
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// @desc    Delete a product
// @route   DELETE /api/products/:id
// @access  Private/Admin
router.delete('/:id', protect, admin, async (req, res) => {
  try {
    const product = await Product.findById(req.params.id);
    
    if (product) {
      await Product.deleteOne({ _id: product._id });
      res.json({ message: 'Product removed' });
    } else {
      res.status(404).json({ message: 'Product not found' });
    }
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

// @desc    Get specification template for a category
// @route   GET /api/products/specs/template/:category
// @access  Public
router.get('/specs/template/:category', (req, res) => {
  try {
    const { category } = req.params;
    const template = getSpecificationTemplate(category);
    res.json({
      category,
      template,
      availableKeys: getSpecificationKeys(category)
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// @desc    Validate product specifications
// @route   POST /api/products/specs/validate
// @access  Public
router.post('/specs/validate', (req, res) => {
  try {
    const { category, specifications } = req.body;

    if (!category || !specifications) {
      return res.status(400).json({
        message: 'Category and specifications are required'
      });
    }

    const validation = validateSpecifications(category, specifications);
    res.json({
      category,
      isValid: validation.isValid,
      errors: validation.errors,
      template: getSpecificationTemplate(category)
    });
  } catch (error) {
    res.status(400).json({ message: error.message });
  }
});

// @desc    Get products by category with their specification structure
// @route   GET /api/products/category/:category
// @access  Public
router.get('/category/:category', async (req, res) => {
  try {
    const { category } = req.params;
    const products = await Product.find({ category });
    const template = getSpecificationTemplate(category);

    res.json({
      category,
      products,
      specificationTemplate: template,
      count: products.length
    });
  } catch (error) {
    res.status(500).json({ message: error.message });
  }
});

export default router;
