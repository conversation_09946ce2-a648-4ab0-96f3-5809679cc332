import express from 'express';
import Wishlist from '../models/wishlistModel.js';
import Product from '../models/productModel.js';
import { protect } from '../middleware/auth.js';

const router = express.Router();

// @desc    Get user's wishlist
// @route   GET /api/wishlist
// @access  Private
router.get('/', protect, async (req, res) => {
  try {
    const userId = req.user.id;
    const wishlistItems = await Wishlist.getUserWishlist(userId);
    
    res.json({
      success: true,
      wishlist: wishlistItems,
      count: wishlistItems.length
    });
  } catch (error) {
    console.error('Error fetching wishlist:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch wishlist',
      error: error.message
    });
  }
});

// @desc    Add product to wishlist
// @route   POST /api/wishlist
// @access  Private
router.post('/', protect, async (req, res) => {
  try {
    const { productId } = req.body;
    const userId = req.user.id;

    // Validate productId
    if (!productId) {
      return res.status(400).json({
        success: false,
        message: 'Product ID is required'
      });
    }

    // Check if product exists
    const product = await Product.findById(productId);
    if (!product) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    // Check if product is already in wishlist
    const existingItem = await Wishlist.isInUserWishlist(userId, productId);
    if (existingItem) {
      return res.status(400).json({
        success: false,
        message: 'Product is already in your wishlist'
      });
    }

    // Add to wishlist
    const wishlistItem = new Wishlist({
      user: userId,
      product: productId
    });

    const savedItem = await wishlistItem.save();
    const populatedItem = await Wishlist.findById(savedItem._id).populate('product');

    res.status(201).json({
      success: true,
      message: 'Product added to wishlist',
      data: populatedItem
    });
  } catch (error) {
    console.error('Error adding to wishlist:', error);
    
    // Handle duplicate key error (in case of race condition)
    if (error.code === 11000) {
      return res.status(400).json({
        success: false,
        message: 'Product is already in your wishlist'
      });
    }

    res.status(500).json({
      success: false,
      message: 'Failed to add product to wishlist',
      error: error.message
    });
  }
});

// @desc    Remove product from wishlist
// @route   DELETE /api/wishlist/:productId
// @access  Private
router.delete('/:productId', protect, async (req, res) => {
  try {
    const { productId } = req.params;
    const userId = req.user.id;

    // Validate productId
    if (!productId) {
      return res.status(400).json({
        success: false,
        message: 'Product ID is required'
      });
    }

    // Remove from wishlist
    const deletedItem = await Wishlist.findOneAndDelete({
      user: userId,
      product: productId
    });

    if (!deletedItem) {
      return res.status(404).json({
        success: false,
        message: 'Product not found in your wishlist'
      });
    }

    res.json({
      success: true,
      message: 'Product removed from wishlist'
    });
  } catch (error) {
    console.error('Error removing from wishlist:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to remove product from wishlist',
      error: error.message
    });
  }
});

// @desc    Clear entire wishlist
// @route   DELETE /api/wishlist
// @access  Private
router.delete('/', protect, async (req, res) => {
  try {
    const userId = req.user.id;
    
    const result = await Wishlist.clearUserWishlist(userId);
    
    res.json({
      success: true,
      message: `Removed ${result.deletedCount} items from wishlist`
    });
  } catch (error) {
    console.error('Error clearing wishlist:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to clear wishlist',
      error: error.message
    });
  }
});

// @desc    Check if product is in wishlist
// @route   GET /api/wishlist/check/:productId
// @access  Private
router.get('/check/:productId', protect, async (req, res) => {
  try {
    const { productId } = req.params;
    const userId = req.user.id;

    const isInWishlist = await Wishlist.isInUserWishlist(userId, productId);
    
    res.json({
      success: true,
      inWishlist: !!isInWishlist
    });
  } catch (error) {
    console.error('Error checking wishlist:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to check wishlist',
      error: error.message
    });
  }
});

// @desc    Get wishlist count
// @route   GET /api/wishlist/count
// @access  Private
router.get('/count', protect, async (req, res) => {
  try {
    const userId = req.user.id;
    const count = await Wishlist.getUserWishlistCount(userId);
    
    res.json({
      success: true,
      count
    });
  } catch (error) {
    console.error('Error getting wishlist count:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to get wishlist count',
      error: error.message
    });
  }
});

export default router;
