import nodemailer from 'nodemailer';
import dotenv from 'dotenv';

dotenv.config();

class EmailService {
  constructor() {
    this.emailEnabled = process.env.EMAIL_USER && process.env.EMAIL_PASS &&
                       process.env.EMAIL_USER !== '<EMAIL>' &&
                       process.env.EMAIL_PASS !== 'your-app-password';

    if (this.emailEnabled) {
      this.transporter = nodemailer.createTransport({
        service: 'gmail',
        auth: {
          user: process.env.EMAIL_USER,
          pass: process.env.EMAIL_PASS
        }
      });
    } else {
      console.log('⚠️  Email not configured. Repair requests will be logged but not emailed.');
    }
  }

  async sendRepairRequest(repairData) {
    try {
      // Log the repair request regardless of email configuration
      console.log('\n🔧 NEW REPAIR REQUEST RECEIVED:');
      console.log('=====================================');
      console.log(`Request Number: ${repairData.requestNumber || 'N/A'}`);
      console.log(`Customer: ${repairData.name}`);
      console.log(`Email: ${repairData.email}`);
      console.log(`Phone: ${repairData.phone}`);
      console.log(`Service: ${repairData.serviceType} - ${repairData.repairType}`);
      console.log(`Estimated Cost: $${repairData.estimatedCost || 0}`);
      console.log(`Description: ${repairData.description}`);
      if (repairData.laptopBrand) console.log(`Laptop: ${repairData.laptopBrand} ${repairData.laptopModel || ''}`);
      if (repairData.desktopType) console.log(`Desktop: ${repairData.desktopType}`);
      if (repairData.tabletBrand) console.log(`Tablet: ${repairData.tabletBrand} ${repairData.tabletModel || ''}`);
      if (repairData.phoneBrand) console.log(`Phone: ${repairData.phoneBrand} ${repairData.phoneModel || ''}`);
      console.log('=====================================\n');

      if (!this.emailEnabled) {
        console.log('📧 Email not configured - repair request logged only');
        return {
          success: true,
          messageId: `logged-${Date.now()}`,
          emailSent: false,
          message: 'Repair request received and logged (email not configured)'
        };
      }
      const {
        name,
        email,
        phone,
        serviceType,
        repairType,
        description,
        laptopBrand,
        laptopModel,
        laptopAge,
        desktopType,
        operatingSystem,
        tabletBrand,
        tabletModel,
        tabletIssue,
        phoneBrand,
        phoneModel,
        phoneCarrier,
        attachments = []
      } = repairData;

      // Create HTML email content
      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h1 style="color: #2563eb; text-align: center; margin-bottom: 30px;">🔧 New Repair Request</h1>

            ${repairData.requestNumber ? `
            <div style="background-color: #dbeafe; padding: 15px; border-radius: 8px; margin-bottom: 20px; text-align: center;">
              <h3 style="color: #1e40af; margin: 0;">Request #${repairData.requestNumber}</h3>
              ${repairData.estimatedCost ? `<p style="color: #1e40af; margin: 5px 0 0 0;">Estimated Cost: <strong>$${repairData.estimatedCost}</strong></p>` : ''}
            </div>
            ` : ''}

            <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h2 style="color: #1e40af; margin-top: 0;">Customer Information</h2>
              <p><strong>Name:</strong> ${name}</p>
              <p><strong>Email:</strong> ${email}</p>
              <p><strong>Phone:</strong> ${phone}</p>
            </div>

            <div style="background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h2 style="color: #1e40af; margin-top: 0;">Service Details</h2>
              <p><strong>Service Type:</strong> ${serviceType}</p>
              <p><strong>Repair Type:</strong> ${repairType}</p>
              <p><strong>Description:</strong></p>
              <div style="background-color: white; padding: 15px; border-radius: 5px; border-left: 4px solid #2563eb;">
                ${description}
              </div>
            </div>

            ${this.generateDeviceSpecificSection(repairData)}

            <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h2 style="color: #92400e; margin-top: 0;">📎 Attachments</h2>
              ${attachments.length > 0 
                ? `<p>${attachments.length} file(s) attached to this request.</p>`
                : '<p>No attachments provided.</p>'
              }
            </div>

            <div style="text-align: center; margin-top: 30px; padding: 20px; background-color: #ecfdf5; border-radius: 8px;">
              <p style="color: #065f46; margin: 0; font-weight: bold;">
                Please respond to this repair request as soon as possible.
              </p>
              <p style="color: #065f46; margin: 5px 0 0 0; font-size: 14px;">
                Customer contact: <a href="mailto:${email}" style="color: #2563eb;">${email}</a> | 
                <a href="tel:${phone}" style="color: #2563eb;">${phone}</a>
              </p>
            </div>
          </div>
          
          <div style="text-align: center; margin-top: 20px; color: #6b7280; font-size: 12px;">
            <p>This email was sent from TechStore Genius Repair Request System</p>
          </div>
        </div>
      `;

      const mailOptions = {
        from: process.env.EMAIL_USER || '<EMAIL>',
        to: '<EMAIL>',
        subject: `🔧 New Repair Request ${repairData.requestNumber ? `#${repairData.requestNumber}` : ''} - ${serviceType} (${repairType})`,
        html: htmlContent,
        replyTo: email
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('📧 Repair request email sent <NAME_EMAIL>:', result.messageId);
      return {
        success: true,
        messageId: result.messageId,
        emailSent: true,
        message: 'Repair request sent successfully via email'
      };

    } catch (error) {
      console.error('Error sending repair request email:', error);
      throw new Error('Failed to send repair request email');
    }
  }

  generateDeviceSpecificSection(repairData) {
    const { serviceType, laptopBrand, laptopModel, laptopAge, desktopType, operatingSystem, 
            tabletBrand, tabletModel, tabletIssue, phoneBrand, phoneModel, phoneCarrier } = repairData;

    let deviceSection = '';

    if (serviceType === 'laptop' && (laptopBrand || laptopModel || laptopAge)) {
      deviceSection = `
        <div style="background-color: #f3e8ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h2 style="color: #7c3aed; margin-top: 0;">💻 Laptop Details</h2>
          ${laptopBrand ? `<p><strong>Brand:</strong> ${laptopBrand}</p>` : ''}
          ${laptopModel ? `<p><strong>Model:</strong> ${laptopModel}</p>` : ''}
          ${laptopAge ? `<p><strong>Age:</strong> ${laptopAge}</p>` : ''}
        </div>
      `;
    } else if (serviceType === 'desktop' && (desktopType || operatingSystem)) {
      deviceSection = `
        <div style="background-color: #f3e8ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h2 style="color: #7c3aed; margin-top: 0;">🖥️ Desktop Details</h2>
          ${desktopType ? `<p><strong>Type:</strong> ${desktopType}</p>` : ''}
          ${operatingSystem ? `<p><strong>Operating System:</strong> ${operatingSystem}</p>` : ''}
        </div>
      `;
    } else if (serviceType === 'tablet' && (tabletBrand || tabletModel || tabletIssue)) {
      deviceSection = `
        <div style="background-color: #f3e8ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h2 style="color: #7c3aed; margin-top: 0;">📱 Tablet Details</h2>
          ${tabletBrand ? `<p><strong>Brand:</strong> ${tabletBrand}</p>` : ''}
          ${tabletModel ? `<p><strong>Model:</strong> ${tabletModel}</p>` : ''}
          ${tabletIssue ? `<p><strong>Issue:</strong> ${tabletIssue}</p>` : ''}
        </div>
      `;
    } else if (serviceType === 'phone' && (phoneBrand || phoneModel || phoneCarrier)) {
      deviceSection = `
        <div style="background-color: #f3e8ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
          <h2 style="color: #7c3aed; margin-top: 0;">📱 Phone Details</h2>
          ${phoneBrand ? `<p><strong>Brand:</strong> ${phoneBrand}</p>` : ''}
          ${phoneModel ? `<p><strong>Model:</strong> ${phoneModel}</p>` : ''}
          ${phoneCarrier ? `<p><strong>Carrier:</strong> ${phoneCarrier}</p>` : ''}
        </div>
      `;
    }

    return deviceSection;
  }

  async sendConfirmationEmail(customerEmail, repairData) {
    try {
      if (!this.emailEnabled) {
        console.log('📧 Confirmation email skipped (email not configured)');
        return { success: false, error: 'Email not configured' };
      }
      const { name, serviceType, repairType } = repairData;

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h1 style="color: #2563eb; text-align: center; margin-bottom: 30px;">✅ Repair Request Confirmed</h1>
            
            <p>Dear ${name},</p>
            
            <p>Thank you for submitting your repair request. We have received your request for <strong>${serviceType} ${repairType}</strong> and will contact you shortly to schedule your repair service.</p>
            
            <div style="background-color: #ecfdf5; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #065f46; margin-top: 0;">What happens next?</h3>
              <ul style="color: #065f46;">
                <li>Our technician will review your request</li>
                <li>We'll contact you within 24 hours</li>
                <li>We'll schedule a convenient time for your repair</li>
                <li>Our expert will fix your device</li>
              </ul>
            </div>
            
            <p>If you have any questions, please don't hesitate to contact us.</p>
            
            <p>Best regards,<br>
            <strong>TechStore Genius Repair Team</strong></p>
          </div>
        </div>
      `;

      const mailOptions = {
        from: process.env.EMAIL_USER || '<EMAIL>',
        to: customerEmail,
        subject: '✅ Repair Request Confirmation - TechStore Genius',
        html: htmlContent
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Confirmation email sent successfully:', result.messageId);
      return { success: true, messageId: result.messageId };

    } catch (error) {
      console.error('Error sending confirmation email:', error);
      // Don't throw error for confirmation email failure
      return { success: false, error: error.message };
    }
  }

  async sendContactMessage(contactData) {
    try {
      // Log the contact message regardless of email configuration
      console.log('\n📧 NEW CONTACT MESSAGE RECEIVED:');
      console.log('=====================================');
      console.log(`Name: ${contactData.name}`);
      console.log(`Email: ${contactData.email}`);
      console.log(`Subject: ${contactData.subject}`);
      console.log(`Message: ${contactData.message}`);
      console.log(`Submitted: ${contactData.submittedAt}`);
      console.log('=====================================\n');

      if (!this.emailEnabled) {
        console.log('📧 Email not configured - contact message logged only');
        return {
          success: true,
          messageId: `logged-${Date.now()}`,
          emailSent: false,
          message: 'Contact message received and logged (email not configured)'
        };
      }

      // Create HTML email content for contact message
      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h1 style="color: #2563eb; text-align: center; margin-bottom: 30px;">📧 New Contact Message</h1>

            <div style="background-color: #f8fafc; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h2 style="color: #1e40af; margin-top: 0;">Contact Information</h2>
              <p><strong>Name:</strong> ${contactData.name}</p>
              <p><strong>Email:</strong> ${contactData.email}</p>
              <p><strong>Subject:</strong> ${contactData.subject}</p>
            </div>

            <div style="background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h2 style="color: #1e40af; margin-top: 0;">Message</h2>
              <div style="background-color: white; padding: 15px; border-radius: 5px; border-left: 4px solid #2563eb; line-height: 1.6;">
                ${contactData.message.replace(/\n/g, '<br>')}
              </div>
            </div>

            <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h2 style="color: #92400e; margin-top: 0;">📊 Submission Details</h2>
              <p><strong>Submitted:</strong> ${new Date(contactData.submittedAt).toLocaleString()}</p>
              <p><strong>IP Address:</strong> ${contactData.ipAddress}</p>
              <p><strong>User Agent:</strong> ${contactData.userAgent}</p>
            </div>

            <div style="text-align: center; margin-top: 30px; padding: 20px; background-color: #ecfdf5; border-radius: 8px;">
              <p style="color: #065f46; margin: 0; font-weight: bold;">
                Please respond to this contact message as soon as possible.
              </p>
              <p style="color: #065f46; margin: 5px 0 0 0; font-size: 14px;">
                Reply directly to: <a href="mailto:${contactData.email}" style="color: #2563eb;">${contactData.email}</a>
              </p>
            </div>
          </div>

          <div style="text-align: center; margin-top: 20px; color: #6b7280; font-size: 12px;">
            <p>This email was sent from TechStore Genius Contact Form</p>
          </div>
        </div>
      `;

      const mailOptions = {
        from: process.env.EMAIL_USER || '<EMAIL>',
        to: '<EMAIL>',
        subject: `📧 New Contact: ${contactData.subject}`,
        html: htmlContent,
        replyTo: contactData.email
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('📧 Contact message email sent <NAME_EMAIL>:', result.messageId);
      return {
        success: true,
        messageId: result.messageId,
        emailSent: true,
        message: 'Contact message sent successfully via email'
      };

    } catch (error) {
      console.error('Error sending contact message email:', error);
      throw new Error('Failed to send contact message email');
    }
  }

  async sendContactConfirmation(customerEmail, contactData) {
    try {
      if (!this.emailEnabled) {
        console.log('📧 Contact confirmation email skipped (email not configured)');
        return { success: false, error: 'Email not configured' };
      }

      const { name, subject } = contactData;

      const htmlContent = `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
          <div style="background-color: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h1 style="color: #2563eb; text-align: center; margin-bottom: 30px;">✅ Message Received</h1>

            <p>Dear ${name},</p>

            <p>Thank you for contacting TechStore Genius. We have received your message regarding "<strong>${subject}</strong>" and will get back to you as soon as possible.</p>

            <div style="background-color: #ecfdf5; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #065f46; margin-top: 0;">What happens next?</h3>
              <ul style="color: #065f46;">
                <li>Our support team will review your message</li>
                <li>We'll respond within 24 hours during business days</li>
                <li>For urgent matters, please call us at +1 (555) 123-4567</li>
                <li>You can also visit our store during business hours</li>
              </ul>
            </div>

            <p>If you have any additional questions, please don't hesitate to contact us.</p>

            <p>Best regards,<br>
            <strong>TechStore Genius Support Team</strong></p>
          </div>
        </div>
      `;

      const mailOptions = {
        from: process.env.EMAIL_USER || '<EMAIL>',
        to: customerEmail,
        subject: '✅ Message Received - TechStore Genius',
        html: htmlContent
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('📧 Contact confirmation email sent successfully:', result.messageId);
      return { success: true, messageId: result.messageId };

    } catch (error) {
      console.error('Error sending contact confirmation email:', error);
      // Don't throw error for confirmation email failure
      return { success: false, error: error.message };
    }
  }
}

export default new EmailService();
