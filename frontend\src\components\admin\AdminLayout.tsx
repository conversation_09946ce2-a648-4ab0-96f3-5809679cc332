
import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { LayoutDashboard, Users, ShoppingBag, Settings, LogOut, Package, Wrench } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import NotificationsPopover from './NotificationsPopover';
import { 
  SidebarProvider, 
  Sidebar, 
  SidebarContent, 
  SidebarHeader, 
  SidebarFooter, 
  SidebarMenu, 
  SidebarMenuItem, 
  SidebarMenuButton, 
  SidebarTrigger 
} from '@/components/ui/sidebar';

interface AdminLayoutProps {
  children: React.ReactNode;
}

const AdminLayout: React.FC<AdminLayoutProps> = ({ children }) => {
  const { logout, user } = useAuth();
  const location = useLocation();

  const menuItems = [
    { title: 'Dashboard', path: '/admin', icon: LayoutDashboard },
    { title: 'Users', path: '/admin/users', icon: Users },
    { title: 'Products', path: '/admin/products', icon: ShoppingBag },
    { title: 'Orders', path: '/admin/orders', icon: Package },
    { title: 'Repair Requests', path: '/admin/repairs', icon: Wrench },
    { title: 'Settings', path: '/admin/settings', icon: Settings },
  ];

  return (
    <SidebarProvider>
      <div className="flex min-h-screen w-full bg-muted/20">
        <Sidebar>
          <SidebarHeader className="flex flex-col items-center justify-center p-4 border-b">
            <Link to="/" className="mb-2">
              <h1 className="text-xl font-bold">Admin Panel</h1>
            </Link>
            {user && (
              <div className="text-sm text-muted-foreground">
                Logged in as: {user.name}
              </div>
            )}
          </SidebarHeader>
          <SidebarContent>
            <SidebarMenu>
              {menuItems.map((item) => (
                <SidebarMenuItem key={item.path}>
                  <SidebarMenuButton 
                    asChild 
                    isActive={location.pathname === item.path}
                    tooltip={item.title}
                  >
                    <Link to={item.path}>
                      <item.icon className="h-5 w-5" />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarContent>
          <SidebarFooter className="border-t">
            <Button 
              variant="ghost" 
              className="w-full justify-start p-2 mt-2" 
              onClick={logout}
            >
              <LogOut className="mr-2 h-5 w-5" />
              Logout
            </Button>
          </SidebarFooter>
        </Sidebar>
        
        <div className="flex flex-col flex-1 p-4 md:p-6">
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-bold">
              {menuItems.find(item => item.path === location.pathname)?.title || 'Dashboard'}
            </h1>
            <div className="flex items-center gap-2">
              <NotificationsPopover />
              <SidebarTrigger />
            </div>
          </div>
          <main className="flex-1 bg-background rounded-lg p-4 shadow">
            {children}
          </main>
        </div>
      </div>
    </SidebarProvider>
  );
};

export default AdminLayout;
