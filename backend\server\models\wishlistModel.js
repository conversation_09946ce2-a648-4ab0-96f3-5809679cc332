import mongoose from 'mongoose';

const wishlistSchema = mongoose.Schema(
  {
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
    },
    product: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Product',
      required: true,
    },
  },
  {
    timestamps: true,
  }
);

// Create compound index to ensure a user can't add the same product twice
wishlistSchema.index({ user: 1, product: 1 }, { unique: true });

// Static method to get user's wishlist with populated product data
wishlistSchema.statics.getUserWishlist = function(userId) {
  return this.find({ user: userId })
    .populate('product')
    .sort({ createdAt: -1 });
};

// Static method to check if a product is in user's wishlist
wishlistSchema.statics.isInUserWishlist = function(userId, productId) {
  return this.findOne({ user: userId, product: productId });
};

// Static method to get wishlist count for a user
wishlistSchema.statics.getUserWishlistCount = function(userId) {
  return this.countDocuments({ user: userId });
};

// Static method to remove all items from user's wishlist
wishlistSchema.statics.clearUserWishlist = function(userId) {
  return this.deleteMany({ user: userId });
};

const Wishlist = mongoose.model('Wishlist', wishlistSchema);

export default Wishlist;
