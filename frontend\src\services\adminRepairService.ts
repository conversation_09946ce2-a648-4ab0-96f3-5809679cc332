import api from './api';

export interface RepairRequestItem {
  _id: string;
  name: string;
  email: string;
  phone: string;
  serviceType: string;
  repairType: string;
  description: string;
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
  estimatedCost: number;
  actualCost: number;
  requestNumber: string;
  emailSent: boolean;
  emailMessageId?: string;
  
  // Device-specific fields
  laptopBrand?: string;
  laptopModel?: string;
  laptopAge?: string;
  desktopType?: string;
  operatingSystem?: string;
  tabletBrand?: string;
  tabletModel?: string;
  tabletIssue?: string;
  phoneBrand?: string;
  phoneModel?: string;
  phoneCarrier?: string;
  
  // Management fields
  technicianNotes: string;
  customerNotes: string;
  assignedTechnician?: {
    _id: string;
    name: string;
    email: string;
  };
  
  createdAt: string;
  updatedAt: string;
}

export interface RepairRequestsResponse {
  success: boolean;
  data: {
    requests: RepairRequestItem[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
    };
  };
}

export interface RepairRequestResponse {
  success: boolean;
  data: RepairRequestItem;
}

export interface RepairStatsResponse {
  success: boolean;
  data: {
    totalRequests: number;
    statusBreakdown: {
      pending: number;
      inProgress: number;
      completed: number;
      cancelled: number;
    };
    detailedStats: Array<{
      _id: string;
      count: number;
      totalEstimatedCost: number;
      totalActualCost: number;
    }>;
  };
}

export interface UpdateStatusRequest {
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'CANCELLED';
  notes?: string;
}

export interface AssignTechnicianRequest {
  technicianId: string;
  notes?: string;
}

class AdminRepairService {
  // Get all repair requests with pagination and filtering
  async getRepairRequests(
    page: number = 1,
    limit: number = 10,
    status?: string,
    priority?: string
  ): Promise<RepairRequestsResponse> {
    try {
      const params = new URLSearchParams({
        page: page.toString(),
        limit: limit.toString(),
      });
      
      if (status && status !== 'all') {
        params.append('status', status);
      }
      
      if (priority && priority !== 'all') {
        params.append('priority', priority);
      }

      const response = await api.get<RepairRequestsResponse>(`/repair/requests?${params}`);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching repair requests:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch repair requests');
    }
  }

  // Get single repair request
  async getRepairRequest(id: string): Promise<RepairRequestResponse> {
    try {
      const response = await api.get<RepairRequestResponse>(`/repair/requests/${id}`);
      return response.data;
    } catch (error: any) {
      console.error('Error fetching repair request:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch repair request');
    }
  }

  // Update repair request status
  async updateStatus(id: string, data: UpdateStatusRequest): Promise<void> {
    try {
      await api.put(`/repair/requests/${id}/status`, data);
    } catch (error: any) {
      console.error('Error updating repair request status:', error);
      throw new Error(error.response?.data?.message || 'Failed to update repair request status');
    }
  }

  // Assign technician to repair request
  async assignTechnician(id: string, data: AssignTechnicianRequest): Promise<void> {
    try {
      await api.put(`/repair/requests/${id}/assign`, data);
    } catch (error: any) {
      console.error('Error assigning technician:', error);
      throw new Error(error.response?.data?.message || 'Failed to assign technician');
    }
  }

  // Get repair statistics
  async getRepairStats(): Promise<RepairStatsResponse> {
    try {
      const response = await api.get<RepairStatsResponse>('/repair/stats');
      return response.data;
    } catch (error: any) {
      console.error('Error fetching repair statistics:', error);
      throw new Error(error.response?.data?.message || 'Failed to fetch repair statistics');
    }
  }

  // Helper method to get status badge variant
  getStatusBadgeVariant(status: string): 'default' | 'secondary' | 'destructive' | 'outline' {
    switch (status) {
      case 'PENDING':
        return 'outline';
      case 'IN_PROGRESS':
        return 'default';
      case 'COMPLETED':
        return 'secondary';
      case 'CANCELLED':
        return 'destructive';
      default:
        return 'outline';
    }
  }

  // Helper method to get priority badge variant
  getPriorityBadgeVariant(priority: string): 'default' | 'secondary' | 'destructive' | 'outline' {
    switch (priority) {
      case 'LOW':
        return 'outline';
      case 'NORMAL':
        return 'secondary';
      case 'HIGH':
        return 'default';
      case 'URGENT':
        return 'destructive';
      default:
        return 'outline';
    }
  }

  // Helper method to format device info
  formatDeviceInfo(request: RepairRequestItem): string {
    const { serviceType } = request;
    
    switch (serviceType) {
      case 'laptop':
        return `${request.laptopBrand || 'Unknown'} ${request.laptopModel || ''}`.trim();
      case 'desktop':
        return `${request.desktopType || 'Desktop'} - ${request.operatingSystem || 'Unknown OS'}`;
      case 'tablet':
        return `${request.tabletBrand || 'Unknown'} ${request.tabletModel || ''}`.trim();
      case 'phone':
        return `${request.phoneBrand || 'Unknown'} ${request.phoneModel || ''}`.trim();
      default:
        return 'Unknown Device';
    }
  }
}

export const adminRepairService = new AdminRepairService();
export default adminRepairService;
